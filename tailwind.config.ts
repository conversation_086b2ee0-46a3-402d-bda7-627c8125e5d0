//@ts-nocheck
import type { Config } from "tailwindcss";

const config: Config = {
    darkMode: ["class"],
    content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
  	extend: {
  		fontFamily: {
  			'sans': ['Inter', 'sans-serif']
  		},
  		backgroundImage: {
  			'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
  			'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
  			'gradient-primary': 'linear-gradient(to right, hsl(24 94% 90% / 0.6), hsl(24 94% 80% / 1))',
  			'gradient-primary-light': 'linear-gradient(to right, hsl(24 94% 95% / 0.3), hsl(24 94% 90% / 0.7))',
  			'gradient-primary-radial': 'radial-gradient(circle, hsl(24 94% 90% / 1) 0%, hsl(24 94% 95% / 0.6) 100%)'
  		},
  		colors: {
  			'custom-border': {
  				DEFAULT: 'hsl(var(--border) / <alpha-value>)',
  				card: 'hsl(var(--border) / <alpha-value>)',
  				input: 'hsl(var(--input) / <alpha-value>)',
  				notification: 'hsl(var(--border) / <alpha-value>)',
  				sidebar: 'hsl(var(--border) / <alpha-value>)'
  			},
  			default: {
  				'50': 'hsl(var(--default-50) / <alpha-value>)',
  				'100': 'hsl(var(--default-100) / <alpha-value>)',
  				'200': 'hsl(var(--default-200) / <alpha-value>)',
  				'300': 'hsl(var(--default-300) / <alpha-value>)',
  				'400': 'hsl(var(--default-400) / <alpha-value>)',
  				'500': 'hsl(var(--default-500) / <alpha-value>)',
  				'600': 'hsl(var(--default-600) / <alpha-value>)',
  				'700': 'hsl(var(--default-700) / <alpha-value>)',
  				'800': 'hsl(var(--default-800) / <alpha-value>)',
  				'900': 'hsl(var(--default-900) / <alpha-value>)',
  				'950': 'hsl(var(--default-950) / <alpha-value>)'
  			},
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			primary: {
  				'50': 'hsl(var(--primary-50))',
  				'100': 'hsl(var(--primary-100))',
  				'200': 'hsl(var(--primary-200))',
  				'300': 'hsl(var(--primary-300))',
  				'400': 'hsl(var(--primary-400))',
  				'500': 'hsl(var(--primary-500))',
  				'600': 'hsl(var(--primary-600))',
  				'700': 'hsl(var(--primary-700))',
  				'800': 'hsl(var(--primary-800))',
  				'900': 'hsl(var(--primary-900))',
  				'950': 'hsl(var(--primary-950))',
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			destructive: {
  				'50': 'hsl(var(--semantic-red-50) / <alpha-value>)',
  				'100': 'hsl(var(--semantic-red-100) / <alpha-value>)',
  				'200': 'hsl(var(--semantic-red-200) / <alpha-value>)',
  				'300': 'hsl(var(--semantic-red-300) / <alpha-value>)',
  				'400': 'hsl(var(--semantic-red-400) / <alpha-value>)',
  				'500': 'hsl(var(--semantic-red-500) / <alpha-value>)',
  				'600': 'hsl(var(--semantic-red-600) / <alpha-value>)',
  				'700': 'hsl(var(--semantic-red-700) / <alpha-value>)',
  				'800': 'hsl(var(--semantic-red-800) / <alpha-value>)',
  				'900': 'hsl(var(--semantic-red-900) / <alpha-value>)',
  				'950': 'hsl(var(--semantic-red-900) / <alpha-value>)',
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			success: {
  				'50': 'hsl(var(--semantic-green-50) / <alpha-value>)',
  				'100': 'hsl(var(--semantic-green-100) / <alpha-value>)',
  				'200': 'hsl(var(--semantic-green-200) / <alpha-value>)',
  				'300': 'hsl(var(--semantic-green-300) / <alpha-value>)',
  				'400': 'hsl(var(--semantic-green-400) / <alpha-value>)',
  				'500': 'hsl(var(--semantic-green-500) / <alpha-value>)',
  				'600': 'hsl(var(--semantic-green-600) / <alpha-value>)',
  				'700': 'hsl(var(--semantic-green-700) / <alpha-value>)',
  				'800': 'hsl(var(--semantic-green-800) / <alpha-value>)',
  				'900': 'hsl(var(--semantic-green-900) / <alpha-value>)',
  				'950': 'hsl(var(--semantic-green-900) / <alpha-value>)',
  				DEFAULT: 'hsl(var(--success) / <alpha-value>)',
  				foreground: 'hsl(var(--success-foreground) / <alpha-value>)'
  			},
  			info: {
  				'50': 'hsl(var(--semantic-blue-50) / <alpha-value>)',
  				'100': 'hsl(var(--semantic-blue-100) / <alpha-value>)',
  				'200': 'hsl(var(--semantic-blue-200) / <alpha-value>)',
  				'300': 'hsl(var(--semantic-blue-300) / <alpha-value>)',
  				'400': 'hsl(var(--semantic-blue-400) / <alpha-value>)',
  				'500': 'hsl(var(--semantic-blue-500) / <alpha-value>)',
  				'600': 'hsl(var(--semantic-blue-600) / <alpha-value>)',
  				'700': 'hsl(var(--semantic-blue-700) / <alpha-value>)',
  				'800': 'hsl(var(--semantic-blue-800) / <alpha-value>)',
  				'900': 'hsl(var(--semantic-blue-900) / <alpha-value>)',
  				'950': 'hsl(var(--semantic-blue-900) / <alpha-value>)',
  				DEFAULT: 'hsl(var(--info) / <alpha-value>)',
  				foreground: 'hsl(var(--info-foreground) / <alpha-value>)'
  			},
  			warning: {
  				'50': 'hsl(var(--semantic-yellow-50) / <alpha-value>)',
  				'100': 'hsl(var(--semantic-yellow-100) / <alpha-value>)',
  				'200': 'hsl(var(--semantic-yellow-200) / <alpha-value>)',
  				'300': 'hsl(var(--semantic-yellow-300) / <alpha-value>)',
  				'400': 'hsl(var(--semantic-yellow-400) / <alpha-value>)',
  				'500': 'hsl(var(--semantic-yellow-500) / <alpha-value>)',
  				'600': 'hsl(var(--semantic-yellow-600) / <alpha-value>)',
  				'700': 'hsl(var(--semantic-yellow-700) / <alpha-value>)',
  				'800': 'hsl(var(--semantic-yellow-800) / <alpha-value>)',
  				'900': 'hsl(var(--semantic-yellow-900) / <alpha-value>)',
  				'950': 'hsl(var(--semantic-yellow-900) / <alpha-value>)',
  				DEFAULT: 'hsl(var(--warning) / <alpha-value>)',
  				foreground: 'hsl(var(--warning-foreground) / <alpha-value>)'
  			},
  			'custom-text': {
  				primary: 'hsl(var(--foreground) / <alpha-value>)',
  				secondary: 'hsl(var(--muted-foreground) / <alpha-value>)',
  				tertiary: 'hsl(var(--muted-foreground) / <alpha-value>)',
  				muted: 'hsl(var(--muted-foreground) / <alpha-value>)',
  				info: 'hsl(var(--info) / <alpha-value>)',
  				success: 'hsl(var(--success) / <alpha-value>)',
  				warning: 'hsl(var(--warning) / <alpha-value>)',
  				destructive: 'hsl(var(--destructive) / <alpha-value>)',
  				notification: 'hsl(var(--muted-foreground) / <alpha-value>)'
  			},
  			'custom-bg': {
  				primary: 'hsl(var(--background) / <alpha-value>)',
  				secondary: 'hsl(var(--muted) / <alpha-value>)',
  				muted: 'hsl(var(--muted) / <alpha-value>)',
  				accent: 'hsl(var(--accent) / <alpha-value>)',
  				card: 'hsl(var(--card) / <alpha-value>)',
  				notification: {
  					unread: 'hsl(var(--primary-50) / <alpha-value>)',
  					default: 'hsl(var(--muted) / <alpha-value>)'
  				}
  			},
  			'custom-primary': {
  				DEFAULT: 'hsl(var(--primary) / <alpha-value>)',
  				hover: 'hsl(var(--primary-600) / <alpha-value>)'
  			},
  			'custom-accent': {
  				blue: 'hsl(var(--primary-300) / <alpha-value>)',
  				orange: 'hsl(var(--primary-300) / <alpha-value>)'
  			},
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
			/* Semantic colors for replacing hardcoded colors */
			'semantic-blue': {
				'50': 'hsl(var(--semantic-blue-50) / <alpha-value>)',
				'100': 'hsl(var(--semantic-blue-100) / <alpha-value>)',
				'200': 'hsl(var(--semantic-blue-200) / <alpha-value>)',
				'300': 'hsl(var(--semantic-blue-300) / <alpha-value>)',
				'400': 'hsl(var(--semantic-blue-400) / <alpha-value>)',
				'500': 'hsl(var(--semantic-blue-500) / <alpha-value>)',
				'600': 'hsl(var(--semantic-blue-600) / <alpha-value>)',
				'700': 'hsl(var(--semantic-blue-700) / <alpha-value>)',
				'800': 'hsl(var(--semantic-blue-800) / <alpha-value>)',
				'900': 'hsl(var(--semantic-blue-900) / <alpha-value>)'
			},
			'semantic-green': {
				'50': 'hsl(var(--semantic-green-50) / <alpha-value>)',
				'100': 'hsl(var(--semantic-green-100) / <alpha-value>)',
				'200': 'hsl(var(--semantic-green-200) / <alpha-value>)',
				'300': 'hsl(var(--semantic-green-300) / <alpha-value>)',
				'400': 'hsl(var(--semantic-green-400) / <alpha-value>)',
				'500': 'hsl(var(--semantic-green-500) / <alpha-value>)',
				'600': 'hsl(var(--semantic-green-600) / <alpha-value>)',
				'700': 'hsl(var(--semantic-green-700) / <alpha-value>)',
				'800': 'hsl(var(--semantic-green-800) / <alpha-value>)',
				'900': 'hsl(var(--semantic-green-900) / <alpha-value>)'
			},
			'semantic-red': {
				'50': 'hsl(var(--semantic-red-50) / <alpha-value>)',
				'100': 'hsl(var(--semantic-red-100) / <alpha-value>)',
				'200': 'hsl(var(--semantic-red-200) / <alpha-value>)',
				'300': 'hsl(var(--semantic-red-300) / <alpha-value>)',
				'400': 'hsl(var(--semantic-red-400) / <alpha-value>)',
				'500': 'hsl(var(--semantic-red-500) / <alpha-value>)',
				'600': 'hsl(var(--semantic-red-600) / <alpha-value>)',
				'700': 'hsl(var(--semantic-red-700) / <alpha-value>)',
				'800': 'hsl(var(--semantic-red-800) / <alpha-value>)',
				'900': 'hsl(var(--semantic-red-900) / <alpha-value>)'
			},
			'semantic-yellow': {
				'50': 'hsl(var(--semantic-yellow-50) / <alpha-value>)',
				'100': 'hsl(var(--semantic-yellow-100) / <alpha-value>)',
				'200': 'hsl(var(--semantic-yellow-200) / <alpha-value>)',
				'300': 'hsl(var(--semantic-yellow-300) / <alpha-value>)',
				'400': 'hsl(var(--semantic-yellow-400) / <alpha-value>)',
				'500': 'hsl(var(--semantic-yellow-500) / <alpha-value>)',
				'600': 'hsl(var(--semantic-yellow-600) / <alpha-value>)',
				'700': 'hsl(var(--semantic-yellow-700) / <alpha-value>)',
				'800': 'hsl(var(--semantic-yellow-800) / <alpha-value>)',
				'900': 'hsl(var(--semantic-yellow-900) / <alpha-value>)'
			},
			'semantic-purple': {
				'50': 'hsl(var(--semantic-purple-50) / <alpha-value>)',
				'100': 'hsl(var(--semantic-purple-100) / <alpha-value>)',
				'200': 'hsl(var(--semantic-purple-200) / <alpha-value>)',
				'300': 'hsl(var(--semantic-purple-300) / <alpha-value>)',
				'400': 'hsl(var(--semantic-purple-400) / <alpha-value>)',
				'500': 'hsl(var(--semantic-purple-500) / <alpha-value>)',
				'600': 'hsl(var(--semantic-purple-600) / <alpha-value>)',
				'700': 'hsl(var(--semantic-purple-700) / <alpha-value>)',
				'800': 'hsl(var(--semantic-purple-800) / <alpha-value>)',
				'900': 'hsl(var(--semantic-purple-900) / <alpha-value>)'
			},
			'semantic-gray': {
				'50': 'hsl(var(--semantic-gray-50) / <alpha-value>)',
				'100': 'hsl(var(--semantic-gray-100) / <alpha-value>)',
				'200': 'hsl(var(--semantic-gray-200) / <alpha-value>)',
				'300': 'hsl(var(--semantic-gray-300) / <alpha-value>)',
				'400': 'hsl(var(--semantic-gray-400) / <alpha-value>)',
				'500': 'hsl(var(--semantic-gray-500) / <alpha-value>)',
				'600': 'hsl(var(--semantic-gray-600) / <alpha-value>)',
				'700': 'hsl(var(--semantic-gray-700) / <alpha-value>)',
				'800': 'hsl(var(--semantic-gray-800) / <alpha-value>)',
				'900': 'hsl(var(--semantic-gray-900) / <alpha-value>)'
			},
			/* Terminal colors */
			'terminal': {
				green: 'hsl(var(--terminal-green) / <alpha-value>)',
				blue: 'hsl(var(--terminal-blue) / <alpha-value>)',
				cyan: 'hsl(var(--terminal-cyan) / <alpha-value>)',
				purple: 'hsl(var(--terminal-purple) / <alpha-value>)',
				yellow: 'hsl(var(--terminal-yellow) / <alpha-value>)',
				red: 'hsl(var(--terminal-red) / <alpha-value>)'
			},
			/* Graph colors */
			'graph': {
				project: 'hsl(var(--graph-node-project) / <alpha-value>)',
				requirement: 'hsl(var(--graph-node-requirement) / <alpha-value>)',
				architecture: 'hsl(var(--graph-node-architecture) / <alpha-value>)',
				epic: 'hsl(var(--graph-node-epic) / <alpha-value>)',
				task: 'hsl(var(--graph-node-task) / <alpha-value>)',
				file: 'hsl(var(--graph-node-file) / <alpha-value>)',
				class: 'hsl(var(--graph-node-class) / <alpha-value>)'
			}
  		},
  		boxShadow: {
  			'custom-card': '0 0.25rem 0.625rem 0 hsl(var(--semantic-gray-900) / 0.1)',
  			'custom-list': '0 0.125rem 0.25rem hsl(var(--semantic-gray-900) / 0.05)',
  			'custom-tab': '0 0.125rem 0.375rem 0 hsl(var(--semantic-gray-900) / 0.25), 0 0 0 0.03125rem hsl(var(--semantic-gray-900) / 0.08)',
  			'custom-circlular': '0 0 0 0.375rem hsl(var(--primary) / 0.2)',
  			'custom-hover': '0 2px 4px hsl(var(--semantic-gray-900) / 0.05)',
  			'custom-dropdown': '0 4px 6px -1px hsl(var(--semantic-gray-900) / 0.1)'
  		},
  		borderWidth: {
  			'custom-default': '1px',
  			'custom-medium': '2px'
  		},
  		borderRadius: {
  			'custom-sm': '0.25rem',
  			'custom-md': '0.375rem',
  			'custom-lg': '0.5rem',
  			'custom-xl': '1.125rem',
  			'custom-full': '9999px',
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		fontSize: {
  			'custom-xs': '0.6875rem',
  			'custom-sm': '0.75rem',
  			'custom-base': '0.8125rem',
  			'custom-md': '0.875rem',
  			'custom-lg': '1rem',
  			'custom-xl': '1.25rem',
  			'custom-2xl': '1.75rem',
  			'heading-1': '2rem',
  			'heading-2': '1.75rem',
  			'heading-3': '1.5rem',
  			'heading-4': '1.25rem',
  			'heading-5': '1.125rem',
  			'heading-6': '1rem',
  			'body-lg': '1rem',
  			'body': '0.875rem',
  			'body-sm': '0.75rem',
  			'caption': '0.6875rem'
  		},
  		lineHeight: {
  			'custom-tight': '1.125rem',
  			'custom-normal': '1.25rem',
  			'custom-relaxed': '1.3125rem',
  			'custom-loose': '2.625rem',
  			'heading': '1.2',
  			'body': '1.5',
  			'tight': '1.25',
  			'relaxed': '1.625'
  		},
  		padding: {
  			'custom-xs': '0.1875rem',
  			'custom-sm': '0.375rem',
  			'custom-md': '0.5rem',
  			'custom-lg': '0.75rem',
  			'custom-xl': '1rem'
  		},
  		margin: {
  			'custom-xxs': '0.0625rem',
  			'custom-xs': '0.1875rem',
  			'custom-sm': '0.375rem',
  			'custom-md': '0.5rem',
  			'custom-lg': '0.75rem',
  			'custom-xl': '1rem'
  		},
  		gap: {
  			'custom-xs': '0.1875rem',
  			'custom-sm': '0.375rem',
  			'custom-md': '0.5rem',
  			'custom-lg': '0.75rem',
  			'custom-xl': '1rem'
  		},
  		spacing: {
  			'custom-0.75': '0.1875rem',
  			'custom-1': '0.25rem',
  			'custom-1.5': '0.375rem',
  			'custom-2': '0.5rem',
  			'custom-3': '0.75rem',
  			'custom-3.5': '0.875rem',
  			'custom-4': '1rem',
  			'custom-5': '1.25rem',
  			'custom-6': '1.5rem',
  			'custom-7': '1.75rem',
  			'custom-8': '2rem',
  			'custom-9': '2.25rem',
  			'custom-12': '3rem',
  			'custom-16': '4rem',
  			'custom-44': '11rem',
  			'custom-64': '16rem'
  		},
  		size: {
  			'custom-2': '0.5rem',
  			'custom-4': '1rem',
  			'custom-5': '1.25rem',
  			'custom-7': '1.75rem',
  			'custom-8': '2rem'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: 0
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: 0
  				}
  			},
  			slideDownAndFade: {
  				from: {
  					opacity: 0,
  					transform: 'translateY(-0.125rem)'
  				},
  				to: {
  					opacity: 1,
  					transform: 'translateY(0)'
  				}
  			},
  			slideLeftAndFade: {
  				from: {
  					opacity: 0,
  					transform: 'translateX(0.125rem)'
  				},
  				to: {
  					opacity: 1,
  					transform: 'translateX(0)'
  				}
  			},
  			slideUpAndFade: {
  				from: {
  					opacity: 0,
  					transform: 'translateY(0.125rem)'
  				},
  				to: {
  					opacity: 1,
  					transform: 'translateY(0)'
  				}
  			},
  			slideRightAndFade: {
  				from: {
  					opacity: 0,
  					transform: 'translateX(-0.125rem)'
  				},
  				to: {
  					opacity: 1,
  					transform: 'translateX(0)'
  				}
  			},
  			'fade-in-down': {
  				'0%': {
  					opacity: '0',
  					transform: 'translateY(-8px)'
  				},
  				'100%': {
  					opacity: '1',
  					transform: 'translateY(0)'
  				}
  			},
  			'fade-in': {
  				'0%': {
  					opacity: '0'
  				},
  				'100%': {
  					opacity: '1'
  				}
  			},
  			spinSlow: {
  				from: {
  					transform: 'rotate(0deg)'
  				},
  				to: {
  					transform: 'rotate(360deg)'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out',
  			slideDownAndFade: 'slideDownAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
  			slideLeftAndFade: 'slideLeftAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
  			slideUpAndFade: 'slideUpAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
  			slideRightAndFade: 'slideRightAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
  			'fade-in-down': 'fade-in-down 0.3s ease-out',
  			'fade-in': 'fade-in 0.2s ease-out',
  			'spin-slow': 'spinSlow 2s linear infinite'
  		},
  		screens: {
  			'xs': '480px',
  			'sm': '640px',
  			'md': '768px',
  			'lg': '1024px',
  			'xl': '1280px',
  			'2xl': '1536px',
  			'3xl': '1920px',
  			'lg-custom': '64rem' /* 1024px */
  		},
  		backdropBlur: {
  			'xs': '2px',
  			'sm': '4px',
  			'DEFAULT': '8px',
  			'md': '12px',
  			'lg': '16px',
  			'xl': '24px',
  			'2xl': '40px',
  			'3xl': '64px',
  			'enhanced': '8px',
  			'strong': '12px',
  			'modal': '8px',
  			'overlay': '6px',
  			'drawer': '4px'
  		}
  	}
  },
  plugins: [
    require('@tailwindcss/forms'),
    require("tailwindcss-animate")
],
};
export default config;
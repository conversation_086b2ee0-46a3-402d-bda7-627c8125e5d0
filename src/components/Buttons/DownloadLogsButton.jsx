// components/DownloadLogsButton.jsx
import React, { useState } from 'react';
import { downloadDiscussionLogs } from '@/utils/api';
import Cookies from 'js-cookie';
import { Download } from 'lucide-react';
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";

const DownloadLogsButton = ({ discussionId }) => {
  const [isDownloading, setIsDownloading] = useState(false);
  const tenantId = Cookies.get('tenant_id');
  const allowedTenants = ['T0005', 'T0015', 'T0002', 'rdk7542'];
  
  // Only render the button for allowed tenants
  if (!allowedTenants.includes(tenantId)) {
    return null;
  }
  
  const handleDownload = async () => {
    setIsDownloading(true);
    try {
      await downloadDiscussionLogs(discussionId);
    } catch (error) {
      console.error('Error downloading logs:', error);
    } finally {
      setIsDownloading(false);
    }
  };
  
  return (
    <BootstrapTooltip title="Download discussion logs" placement="bottom">
      <button
        onClick={handleDownload}
        disabled={isDownloading}
        className="ml-2 text-blue-600 hover:text-blue-800"
        aria-label="Download logs"
      >
        <Download size={16} className={isDownloading ? 'animate-pulse' : ''} />
      </button>
    </BootstrapTooltip>
  );
};

export default DownloadLogsButton;
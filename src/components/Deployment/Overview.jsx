import { useContext, useEffect } from 'react';
import { DiscussionTabContext } from '@/components/Context/DiscussionTabContext';
import Badge from "@/components/UIComponents/Badge/Badge"

export default function Overview() {
    const { overviewData, isLoading, error, fetchOverviewData } = useContext(DiscussionTabContext);
   
    useEffect(() => {
      const urlParams = new URLSearchParams(window.location.search);
      const containerId = urlParams.get('node_id');
      const projectId = window.location.pathname.split('/')[2];
      
      fetchOverviewData(projectId, containerId);
    }, []);
   
    if (isLoading) {
        return (
          <div className="p-4">
            <div className="flex items-center gap-2 mb-4">
              <div className="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
              <div className="h-6 bg-blue-100 rounded w-24 animate-pulse"></div>
            </div>
            <div className="border rounded shadow-sm p-4 space-y-4">
              <div className="flex gap-4">
                <div className="h-5 bg-gray-200 rounded w-20 animate-pulse"></div>
                <div className="h-5 bg-gray-200 rounded w-32 animate-pulse"></div>
              </div>
              <div className="flex gap-4">
                <div className="h-5 bg-gray-200 rounded w-24 animate-pulse"></div>
                <div className="h-5 bg-gray-200 rounded w-64 animate-pulse"></div>
              </div>
            </div>
          </div>
        );
      }
    if (error) return <div>Error: {error}</div>;
    if (!overviewData?.deployments) return null;
   
    return (
      <div className="p-1">
        
        {overviewData.deployments.map((deployment, index) => (
            <>
            <div className='flex'>
           <div>
           <h2 className="project-panel-heading mb-4">Deployment Overview  </h2></div> 
            <div className='ml-2 '>
            <Badge type={deployment.properties.Type} />

            </div>

            </div>
          <div className="mb-4 border rounded shadow-sm">
          <div className="grid grid-cols-[100px_1fr] gap-1">
            <div className="px-2 py-1 font-weight-semibold text-gray-700">Title:</div>
            <div className="px-2 py-1 project-panel-content">{deployment.properties.Title}</div>
            
         
            
            <div className="px-2 py-1 font-weight-semibold text-gray-700">Description:</div>
            <div className="px-2 py-1 project-panel-content">{deployment.properties.Description}</div>
          </div>
         </div>
         </>
        ))}
      </div>
    );
   }
import React, { useState, useEffect } from "react";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import Cookies from "js-cookie";
import { useSearchParams } from "next/navigation";
import { Loading2 } from "../Loaders/Loading";
import { useCodeGeneration } from "../Context/CodeGenerationContext";
import {
  FaTimes,
  FaInfoCircle,
  FaProjectDiagram,
  FaCheckCircle,
  FaSpinner,
  FaClock,
  FaExclamationCircle,
} from "react-icons/fa";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import TaskPlanComponent from "./TaskPlanComponent";
import { getHeadersRaw } from "@/utils/api";

const StepStatusIcon = ({ step, isCompleted }) => {
  const getIcon = () => {
    if (step.reverted) {
      return <FaExclamationCircle className="text-red-500 w-5 h-5" />;
    }
    if (isCompleted) {
      return <FaCheckCircle className="text-green-500 w-5 h-5" />;
    }
    if (step.status === "in_progress") {
      return <FaSpinner className="text-blue-500 w-5 h-5 animate-spin" />;
    }
    return <FaClock className="text-gray-400 w-5 h-5" />;
  };

  return (
    <div className="flex-shrink-0 w-6 flex items-center justify-center">
      {getIcon()}
    </div>
  );
};

const StepDetailsModal = ({ step, onClose }) => (
  <div
    className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 modal-overlay"
    onClick={(e) => {
      if (e.target.classList.contains("modal-overlay")) {
        onClose();
      }
    }}
  >
    <div className="bg-white rounded-lg w-3/4 h-[80vh] flex flex-col relative">
      <div className="flex justify-between items-center p-6 border-b sticky top-0 bg-white rounded-t-lg z-10">
        <h2 className="project-panel-heading pr-8">{step.title}</h2>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          <FaTimes size={24} />
        </button>
      </div>

      <div className="flex-1 overflow-y-auto custom-scrollbar p-6">
        <div className="space-y-4">
          <div>
            <span className="project-panel-heading">Agent:</span> {step.agent}
          </div>
          <div>
            <span className="project-panel-heading">Action:</span> {step.action}
          </div>
          <div>
            <span className="project-panel-heading">Details:</span>
            <pre className="mt-2 p-2 bg-gray-100 rounded overflow-x-auto">
              {JSON.stringify(JSON.parse(step.details), null, 2)}
            </pre>
          </div>
          <div>
            <span className="project-panel-heading">Context:</span>
            <pre className="mt-2 p-2 bg-gray-100 rounded overflow-x-auto">
              {JSON.stringify(step.context, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  </div>
);

const TaskPlanModal = ({ step, onClose }) => (
  <div
    className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 modal-overlay"
    onClick={(e) => {
      if (e.target.classList.contains("modal-overlay")) {
        onClose();
      }
    }}
  >
    <div className="bg-white rounded-lg w-3/4 h-[80vh] flex flex-col relative">
      <div className="flex justify-between items-center p-6 border-b sticky top-0 bg-white rounded-t-lg z-10">
        <h2 className="project-panel-heading pr-8">
          Task Plan for {step.title}
        </h2>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          <FaTimes size={24} />
        </button>
      </div>

      <div className="flex-1 overflow-y-auto p-6">
        <TaskPlanComponent stepId={step.id} />
      </div>
    </div>
  </div>
);

const StepsComponent = () => {
  const { statusData } = useCodeGeneration();
  const [steps, setSteps] = useState([]);
  const [selectedStep, setSelectedStep] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showTaskPlanModal, setShowTaskPlanModal] = useState(false);
  const searchParams = useSearchParams();
  const currentTaskDetailsId = searchParams.get("task_id");
  const idToken = Cookies.get("idToken");
  const tenant_id = Cookies.get("tenant_id");
  const [stepsState, setStepsState] = useState({
    steps: [],
    selectedStep: null,
    isLoading: true,
  });
  const [modalState, setModalState] = useState({
    type: null, // 'details' | 'taskPlan' | null
    isOpen: false,
  });

  useEffect(() => {
    const abortController = new AbortController();
    setIsLoading(true);

    const eventSource = fetchEventSource(
      `${process.env.NEXT_PUBLIC_API_URL}/batch/callback/past_steps/${currentTaskDetailsId}`,
      {
        headers: getHeadersRaw(),
        openWhenHidden: true,
        signal: abortController.signal,
        onmessage: (event) => {
          setIsLoading(false);
          const data = JSON.parse(event.data);
          if (data.past_steps) {
            setSteps(data.past_steps);
            if (data.past_steps.length > 0) {
              setSelectedStep(data.past_steps[0]);
            }
          }
        },
        onerror: (error) => {
          
          setIsLoading(false);
          abortController.abort();
        },
        onclose: () => {
          setIsLoading(false);
        },
      }
    );

    return () => {
      abortController.abort();
    };
  }, [currentTaskDetailsId, idToken]);

  const isStepCompleted = (step) => {
    if (!statusData?.past_statuses) return false;

    const stepTitle = step.title?.toLowerCase() || "";
    const stepAction = step.action?.toLowerCase() || "";

    return statusData.past_statuses.some((status) => {
      const statusLower = status.toLowerCase();
      return (
        statusLower.includes(stepTitle) || statusLower.includes(stepAction)
      );
    });
  };

  if (isLoading) {
    return <Loading2 />;
  }

  if (!steps.length) {
    return (
      <p className="text-center flex justify-center items-center">
        <EmptyStateView type="noStepsAvailable" />
      </p>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="top-0 bg-white pb-4 border-b">
        <div className="flex justify-between items-center px-2">
          <span className="project-panel-heading">Current Steps</span>
          <button
            onClick={() => {
              setSelectedStep(steps[0]);
              setShowTaskPlanModal(true);
            }}
            className="whitespace-nowrap px-4 py-2 text-font bg-green-200 text-green-600 rounded-md hover:bg-green-300 flex items-center gap-2"
          >
            <FaProjectDiagram size={14} />
            <span>View Task Plan</span>
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="space-y-2">
          {[...steps].reverse().map((step, index) => (
            <div
              key={index}
              className={`rounded-lg shadow p-4 transition-all duration-200 ${
                index === 0 ? "bg-blue-50 border border-blue-200" : "bg-white"
              }`}
            >
              <div className="flex items-center">
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <StepStatusIcon
                    step={step}
                    isCompleted={isStepCompleted(step)}
                  />
                  <h3
                    className={`text-font truncate ${
                      step.reverted
                        ? "line-through text-gray-500"
                        : index === 0
                        ? "font-weight-medium"
                        : ""
                    }`}
                  >
                    {step.title}
                    {step.reverted && (
                      <span className="ml-2 typography-caption text-red-500 font-weight-medium">
                        (Reverted)
                      </span>
                    )}
                    {index === 0 && (
                      <span className="ml-2 typography-caption text-blue-600 font-weight-medium">
                        (Current)
                      </span>
                    )}
                  </h3>
                </div>

                <div className="flex items-center ml-4 flex-shrink-0">
                  <button
                    onClick={() => {
                      setSelectedStep(step);
                      setShowDetailsModal(true);
                    }}
                    className="whitespace-nowrap px-3 py-1.5 text-font bg-blue-200 text-blue-600 rounded-md hover:bg-blue-300 flex items-center gap-1.5"
                  >
                    <FaInfoCircle size={14} />
                    <span>View Details</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {showDetailsModal && selectedStep && (
        <StepDetailsModal
          step={selectedStep}
          onClose={() => setShowDetailsModal(false)}
        />
      )}

      {showTaskPlanModal && selectedStep && (
        <TaskPlanModal
          step={selectedStep}
          onClose={() => setShowTaskPlanModal(false)}
        />
      )}
    </div>
  );
};

export default StepsComponent;

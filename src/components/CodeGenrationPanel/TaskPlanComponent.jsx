import React, { useState, useEffect } from "react";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import Cookies from "js-cookie";
import { useSearchParams } from "next/navigation";
import { Loading2 } from "../Loaders/Loading";
import EnParser from "@/utils/enParser";
import en from "@/en.json";
import {
  FaCode,
  FaBug,
  FaTools,
  FaVial,
  FaChartLine,
  FaSearch,
  FaRobot,
} from "react-icons/fa";
import { getHeadersRaw } from "@/utils/api";

const agentIcons = {
  CodeWritingAgent: FaCode,
  TestCodeWritingAgent: FaVial,
  EnvironmentSetupAgent: FaTools,
  TestExecutionAgent: FaBug,
  ExecutionReviewAgent: FaChartLine,
  SearchAgent: FaSearch,
  default: FaRobot,
};

const getStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case "completed":
      return "text-green-700";
    case "in-progress":
      return "text-blue-700";
    case "to-do":
      return "text-gray-600";
    default:
      return "text-gray-600";
  }
};

const getStatusBgColor = (status) => {
  switch (status?.toLowerCase()) {
    case "completed":
      return "bg-green-100";
    case "in-progress":
      return "bg-blue-100";
    case "to-do":
      return "bg-gray-100";
    default:
      return "bg-gray-100";
  }
};

const TaskPlanComponent = () => {
  const [taskPlan, setTaskPlan] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const searchParams = useSearchParams();
  const currentTaskDetailsId = searchParams.get("task_id");
  const idToken = Cookies.get("idToken");
  const tenant_id = Cookies.get("tenant_id");
  useEffect(() => {
    const abortController = new AbortController();
    setIsLoading(true);

    const eventSource = fetchEventSource(
      `${process.env.NEXT_PUBLIC_API_URL}/batch/callback/task_plan/${currentTaskDetailsId}`,
      {
        headers: getHeadersRaw(),
        openWhenHidden: true,
        signal: abortController.signal,
        onmessage: (event) => {
          setIsLoading(false);
          const data = JSON.parse(event.data);
          if (data.task_plan) {
            let parsedTaskPlan = JSON.parse(data.task_plan);
            setTaskPlan(parsedTaskPlan);
          }
        },
        onerror: (error) => {
          
          setIsLoading(false);
          abortController.abort();
        },
        onclose: () => {
          
          setIsLoading(false);
        },
      }
    );

    return () => {
      abortController.abort();
    };
  }, [currentTaskDetailsId, idToken, tenant_id]);

  if (isLoading) {
    return (
        <Loading2 />
    );
  }

  if (!taskPlan || taskPlan.length === 0) {
    return (
      <p className="text-center flex justify-center h-96 items-center">
        <EnParser content={en.NoTaskPlanAvailable} />
      </p>
    );
  }

  return (
    <div className="task-plan-container space-y-4">
      <h3 className="project-panel-heading mb-4">Task Plan</h3>
      {taskPlan.map((task, index) => (
        <div
          key={index}
          className="bg-white border border-gray-200 rounded-md p-4"
        >
          <div className="flex items-center mb-4">
            {React.createElement(
              agentIcons[task.agent] || agentIcons["default"],
              {
                className: "typography-heading-4 mr-3 text-gray-700",
              }
            )}
            <h4 className="project-panel-heading text-gray-800">
              {task.step || "N/A"}
            </h4>
          </div>

          <div className="ml-7 space-y-2">
            <div className="flex items-center">
              <span className="project-panel-heading w-16">Status:</span>
              <span
                className={`px-2 py-0.5 rounded typography-body-sm font-weight-semibold ${getStatusBgColor(
                  task.status
                )} ${getStatusColor(task.status)}`}
              >
                {task.status || "N/A"}
              </span>
            </div>

            <div className="flex items-center">
              <span className="project-panel-heading w-16">Agent:</span>
              <span className="text-gray-600">{task.agent || "N/A"}</span>
            </div>

            <div className="flex items-center">
              <span className="project-panel-heading w-16">Details:</span>
              <span className="text-gray-600">{task.details || "N/A"}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default TaskPlanComponent;

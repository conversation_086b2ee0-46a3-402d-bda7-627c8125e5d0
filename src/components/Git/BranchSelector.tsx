import React, { useState, useEffect, useRef } from 'react';
import { GitBranch, Check, Loader2, Plus, ChevronDown, X, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { DynamicButton } from '../UIComponents/Buttons/DynamicButton';
import { createBranch } from "@/utils/repositoryAPI";

interface CreateBranchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (branchName: string, sourceBranch: string) => Promise<void>;
  branches: any[];
  isLoading: boolean;
}

const CreateBranchModal: React.FC<CreateBranchModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  branches,
  isLoading
}) => {
  const [newBranchName, setNewBranchName] = useState('');
  const [sourceBranch, setSourceBranch] = useState('main');

  // Update source branch when branches change
  useEffect(() => {
    if (branches.length > 0) {
      // Try to find main, master, or use first branch as default
      const defaultBranch = branches.find(b => b.name === 'main') || 
                           branches.find(b => b.name === 'master') || 
                           branches[0];
      setSourceBranch(defaultBranch?.name || 'main');
    }
  }, [branches]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="typography-body-lg font-weight-semibold mb-4">Create New Branch</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block typography-body-sm font-weight-medium text-gray-700 mb-1">
              New Branch Name
            </label>
            <input
              type="text"
              placeholder="feature/my-new-branch"
              value={newBranchName}
              onChange={(e) => setNewBranchName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block typography-body-sm font-weight-medium text-gray-700 mb-1">
              Source Branch
            </label>
            <select
              value={sourceBranch}
              onChange={(e) => setSourceBranch(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {branches.map((branch: any) => (
                <option key={branch.name} value={branch.name}>
                  {branch.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <DynamicButton
            variant="secondary"
            size="small"
            text="Cancel"
            onClick={onClose}
            disabled={isLoading}
          />
          <DynamicButton
            variant="primary"
            size="small"
            text="Create Branch"
            onClick={() => onSubmit(newBranchName, sourceBranch)}
            loading={isLoading}
            disabled={isLoading || !newBranchName.trim()}
          />
        </div>
      </div>
    </div>
  );
};

// Branch Selector Modal component with pagination
interface BranchSelectorModalProps {
  isOpen: boolean;
  onClose: () => void;
  branches: any[];
  currentBranch: string;
  onBranchSelect: (branchName: string) => Promise<void>;
  isLoadingBranches: boolean;
  isBranchSelecting: boolean;
  onCreateBranchClick: () => void;
  pagination: {
    currentPage: number;
    totalPages: number;
    perPage: number;
    totalCount: number;
  };
  onPageChange: (page: number) => void;
}

const BranchSelectorModal: React.FC<BranchSelectorModalProps> = ({
  isOpen,
  onClose,
  branches,
  currentBranch,
  onBranchSelect,
  isLoadingBranches,
  isBranchSelecting,
  onCreateBranchClick,
  pagination,
  onPageChange
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [pageInput, setPageInput] = useState(pagination.currentPage.toString());
  const [pageInputError, setPageInputError] = useState(false);
  const [selectingBranch, setSelectingBranch] = useState<string | null>(null);
  
  // Update page input when pagination changes
  useEffect(() => {
    setPageInput(pagination.currentPage.toString());
    setPageInputError(false);
  }, [pagination.currentPage]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };
    
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    setPageInput(value);
    setPageInputError(false);
  };

  const handlePageInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Handle empty input
    if (!pageInput.trim()) {
      setPageInput(pagination.currentPage.toString());
      return;
    }
    
    const page = parseInt(pageInput, 10);
    
    // Validate page number
    if (page > 0 && page <= pagination.totalPages) {
      onPageChange(page);
    } else {
      setPageInputError(true);
      // Keep the invalid input to show the error state
      setTimeout(() => {
        setPageInput(pagination.currentPage.toString());
        setPageInputError(false);
      }, 1500);
    }
  };

  const handleBranchClick = async (branchName: string) => {
    // Don't do anything if clicking the same branch that's already selected
    if (currentBranch === branchName) {
      return;
    }

    // Don't proceed if already selecting a branch
    if (selectingBranch) {
      return;
    }

    setSelectingBranch(branchName);
    try {
      await onBranchSelect(branchName);
    } finally {
      setSelectingBranch(null);
    }
  };

  if (!isOpen) return null;

  // Determine if pagination should be shown
  const showPagination = !isLoadingBranches && pagination.totalCount > 0 && pagination.totalPages > 1;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div ref={modalRef} className="bg-white rounded-lg p-6 w-full max-w-md max-h-[80vh] flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h2 className="typography-body-lg font-weight-semibold">Select Branch</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="overflow-y-auto flex-grow">
          {isLoadingBranches ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />
            </div>
          ) : branches.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No branches found</p>
            </div>
          ) : (
            <ul className="space-y-1">
              {branches.map((branch) => {
                const isCurrentBranch = currentBranch === branch.name;
                const isSelecting = selectingBranch === branch.name;
                const isDisabled = selectingBranch !== null && !isSelecting;
                const showBlueStyle = (isCurrentBranch && !selectingBranch) || isSelecting;

                return (
                  <li key={branch.name}>
                    <button
                      onClick={() => handleBranchClick(branch.name)}
                      className={`w-full text-left px-3 py-2 rounded-md flex items-center transition-colors ${
                        showBlueStyle
                          ? 'bg-primary-50 text-primary-700'
                          : isDisabled
                          ? 'opacity-50 cursor-not-allowed'
                          : 'hover:bg-gray-50'
                      }`}
                      disabled={isDisabled}
                    >
                      {(isCurrentBranch && !selectingBranch) ? (
                        <Check className="w-4 h-4 mr-2 flex-shrink-0" />
                      ) : (
                        <GitBranch className="w-4 h-4 mr-2 flex-shrink-0" />
                      )}
                      <span className="truncate">{branch.name}</span>
                      {isSelecting && (
                        <Loader2 className="w-4 h-4 ml-auto animate-spin" />
                      )}
                    </button>
                  </li>
                );
              })}
            </ul>
          )}
        </div>
        
        {showPagination && (
          <div className="flex flex-wrap justify-center items-center gap-2 mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center">
              <button
                onClick={() => onPageChange(1)}
                disabled={pagination.currentPage <= 1 || isLoadingBranches}
                className={`p-1 rounded-md ${
                  pagination.currentPage <= 1 || isLoadingBranches
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                aria-label="First page"
              >
                <ChevronsLeft className="w-4 h-4" />
              </button>
              <button
                onClick={() => onPageChange(pagination.currentPage - 1)}
                disabled={pagination.currentPage <= 1 || isLoadingBranches}
                className={`p-1 rounded-md ${
                  pagination.currentPage <= 1 || isLoadingBranches
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                aria-label="Previous page"
              >
                <ChevronLeft className="w-4 h-4" />
              </button>
            </div>
            
            <form onSubmit={handlePageInputSubmit} className="flex items-center mx-1">
              <div className="relative">
                <input
                  type="text"
                  value={pageInput}
                  onChange={handlePageInputChange}
                  className={`w-12 h-8 text-center border rounded-md focus:outline-none ${
                    pageInputError
                      ? 'border-red-500 focus:ring-1 focus:ring-red-500'
                      : 'border-gray-300 focus:ring-1 focus:ring-primary'
                  }`}
                  aria-label="Page number"
                />
                {pageInputError && (
                  <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 typography-caption text-red-500 whitespace-nowrap">
                    Invalid page
                  </div>
                )}
              </div>
              <span className="mx-2 typography-body-sm text-gray-600 whitespace-nowrap">
                of {pagination.totalPages}
              </span>
              <button
                type="submit"
                className="px-2 py-1 typography-caption bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:bg-gray-50 disabled:text-gray-400"
                disabled={isLoadingBranches}
              >
                Go
              </button>
            </form>
            
            <div className="flex items-center">
              <button
                onClick={() => onPageChange(pagination.currentPage + 1)}
                disabled={pagination.currentPage >= pagination.totalPages || isLoadingBranches}
                className={`p-1 rounded-md ${
                  pagination.currentPage >= pagination.totalPages || isLoadingBranches
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                aria-label="Next page"
              >
                <ChevronRight className="w-4 h-4" />
              </button>
              <button
                onClick={() => onPageChange(pagination.totalPages)}
                disabled={pagination.currentPage >= pagination.totalPages || isLoadingBranches}
                className={`p-1 rounded-md ${
                  pagination.currentPage >= pagination.totalPages || isLoadingBranches
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                aria-label="Last page"
              >
                <ChevronsRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
        
        <div className="mt-4 pt-4 border-t border-gray-200">
          <DynamicButton
            variant="primary"
            size="small"
            text="Create New Branch"
            icon={Plus}
            onClick={onCreateBranchClick}
            disabled={isLoadingBranches}
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
};

interface BranchSelectorProps {
  projectId: string | number;
  containerId: string | number;
  currentBranch: string;
  onUpdate: (branch: string) => Promise<void>;
  className?: string;
  // External branch data
  branches: any[];
  pagination: {
    currentPage: number;
    totalPages: number;
    perPage: number;
    totalCount: number;
  };
  onFetchBranches: (page?: number) => Promise<void>;
  isFetchingBranches: boolean;
  repoError: boolean;
  // Add prop for forcing complete refresh
  onForceRefreshBranches?: (branchName?: string) => Promise<void>;
}

export const BranchSelector: React.FC<BranchSelectorProps> = ({
  projectId,
  containerId,
  currentBranch,
  onUpdate,
  className = '',
  branches,
  pagination,
  onFetchBranches,
  isFetchingBranches,
  repoError,
  onForceRefreshBranches
}) => {
  const [selectedBranch, setSelectedBranch] = useState<string>('');
  const [isBranchSelecting, setIsBranchSelecting] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isBranchModalOpen, setIsBranchModalOpen] = useState(false);
  const [userHasSelectedBranch, setUserHasSelectedBranch] = useState(false);

  // Handle initial branch selection when branches are loaded
  useEffect(() => {
    if (branches.length > 0 && !isFetchingBranches && !selectedBranch && !userHasSelectedBranch) {
      let branchToSelect = '';

      // Priority 1: Use currentBranch if it exists in branches
      if (currentBranch && branches.find(b => b.name === currentBranch)) {
        branchToSelect = currentBranch;
      }
      // Priority 2: For setup/configuration modals, default to kavia-main if currentBranch is null/undefined
      else if (!currentBranch || currentBranch === null) {
        const kaviaBranch = branches.find(b => b.name === 'kavia-main');
        if (kaviaBranch) {
          branchToSelect = kaviaBranch.name;
        }
        else {
          // Priority 3: Fallback to main/master if kavia-main doesn't exist
          const fallbackBranch = branches.find(b => b.name === 'main') ||
                                branches.find(b => b.name === 'master');
          
          if (fallbackBranch) {
            branchToSelect = fallbackBranch.name;
          } else if (branches.length > 0) {
            // Priority 4: Use the first branch
            branchToSelect = branches[0].name;
          }
        }
      }

      if (branchToSelect) {
        setSelectedBranch(branchToSelect);
      }
    }
  }, [branches, isFetchingBranches, selectedBranch, currentBranch, userHasSelectedBranch]);

  const handleOpenBranchModal = () => {
    setIsBranchModalOpen(true);
    // Refresh branches when opening modal
    if (onFetchBranches) {
      onFetchBranches(1);
    }
  };

  const handlePageChange = (page: number) => {
    // Validate page number before fetch
    if (page < 1 || page > pagination.totalPages) {
      return;
    }
    if (onFetchBranches) {
      onFetchBranches(page);
    }
  };

  // Add retry logic for branch selection
  const handleBranchSelect = async (branchName: string, retryCount = 0) => {
    if (!branchName || isBranchSelecting) return;
    
    setIsBranchSelecting(true);
    setUserHasSelectedBranch(true); // Mark that user has made a selection
    
    try {
      // Add a small delay to ensure API is ready
      await new Promise(resolve => setTimeout(resolve, 200));
      
      await onUpdate(branchName);
      setSelectedBranch(branchName);
      setIsBranchModalOpen(false);
    } catch (error) {
      console.error("Error selecting branch:", error);
      
      // Retry logic - maximum 3 retries with increasing delay
      if (retryCount < 3) {
        setIsBranchSelecting(false);
        // Wait longer for each retry
        const retryDelay = 1000 * (retryCount + 1);
        
        setTimeout(() => {
          handleBranchSelect(branchName, retryCount + 1);
        }, retryDelay);
        
        return;
      }
      // If all retries failed, still update the selected branch locally
      setSelectedBranch(branchName);
      setIsBranchModalOpen(false);
    } finally {
      setIsBranchSelecting(false);
    }
  };

  const handleCreateBranch = async (newBranchName: string, sourceBranch: string) => {
    setIsBranchSelecting(true);
    try {
      // Create the new branch
      await createBranch(projectId, containerId, newBranchName, sourceBranch);
      
      // Wait a moment for the backend to process the branch creation
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Force a complete refresh of branches with property update
      if (onForceRefreshBranches) {
        // Use the force refresh function with the new branch name
        await onForceRefreshBranches(newBranchName);
      } else if (onFetchBranches) {
        // Fallback to regular fetch
        await onFetchBranches(1);
        // Update the property separately
        await onUpdate(newBranchName);
      }
      
      // Select the newly created branch (this will update the local state)
      setSelectedBranch(newBranchName);
      setIsCreateModalOpen(false);
    } catch (error) {
      console.error("Error creating branch:", error);
      // Even if branch creation fails, refresh the branch list to ensure consistency
      if (onForceRefreshBranches) {
        try {
          await onForceRefreshBranches();
        } catch (refreshError) {
          console.error("Error refreshing branches after failed creation:", refreshError);
        }
      } else if (onFetchBranches) {
        try {
          await onFetchBranches(1);
        } catch (refreshError) {
          console.error("Error refreshing branches after failed creation:", refreshError);
        }
      }
    } finally {
      setIsBranchSelecting(false);
    }
  };

  // Update selected branch when currentBranch prop changes (but don't override user selections)
  useEffect(() => {
    if (currentBranch && currentBranch !== selectedBranch && branches.find(b => b.name === currentBranch) && !userHasSelectedBranch) {
      setSelectedBranch(currentBranch);
    }
  }, [currentBranch, selectedBranch, branches, userHasSelectedBranch]);
  
  if (repoError) {
    return (
      <div className="flex items-center gap-2 px-3 py-1.5 typography-body-sm font-weight-medium text-gray-500 bg-gray-50 border border-gray-200 rounded-md">
        <GitBranch className="w-4 h-4" />
        <span>Repository not available</span>
      </div>
    );
  }

  return (
    <>
      <button 
        onClick={handleOpenBranchModal}
        className={`
          flex items-center justify-between gap-2 px-3 py-1.5 w-full
          typography-body-sm font-weight-medium text-gray-700 
          bg-white border border-gray-300 rounded-md hover:bg-gray-50
          ${isFetchingBranches ? 'border-blue-200 bg-blue-50' : ''}
          transition-colors duration-200
          ${className}
        `}
        disabled={isFetchingBranches}
      >
        <span className='flex gap-2 items-center'>
          {isFetchingBranches ? (
            <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
          ) : (
            <GitBranch className={`
              w-4 h-4
              ${isFetchingBranches ? 'text-blue-400' : ''}
              transition-colors duration-200
            `} />
          )}
          <span className="truncate">
            {isFetchingBranches ? 'Loading branches...' : (selectedBranch || (currentBranch === null ? 'Not configured' : 'Select branch...'))}
          </span>
        </span>
        <ChevronDown size={16} />
      </button>

      <BranchSelectorModal
        isOpen={isBranchModalOpen}
        onClose={() => setIsBranchModalOpen(false)}
        branches={branches}
        currentBranch={selectedBranch || ''}
        onBranchSelect={handleBranchSelect}
        isLoadingBranches={isFetchingBranches}
        isBranchSelecting={isBranchSelecting}
        onCreateBranchClick={() => {
          setIsBranchModalOpen(false);
          setIsCreateModalOpen(true);
        }}
        pagination={pagination}
        onPageChange={handlePageChange}
      />

      <CreateBranchModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateBranch}
        branches={branches}
        isLoading={isBranchSelecting}
      />
    </>
  );
};
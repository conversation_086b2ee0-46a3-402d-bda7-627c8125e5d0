import React from 'react';
import Accordion from '../BrowsePanel/Accordion';
import { useRouter } from 'next/navigation';
import dynamic from "next/dynamic";
import { formatUTCToLocal } from '@/utils/datetime'
import ChartAccordion from '../BrowsePanel/Architecture/ChartAccordion';
import APIDocumentation from '../API/API';

const NoSSR = dynamic(() => import("../Chart/MermaidChart"), { ssr: false });
const PropertiesRenderer = ({ properties, metadata, to_skip = [], to_show = [], projectId = "", architectureId = "" }) => {
  const router = useRouter();
  const params = useParams();

  const transformKey = (key) => {
    return key
      .replace(/_/g, ' ')
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/\b\w/g, char => char.toUpperCase());
  };


  const renderField = (key, value, metadata) => {
    const label = transformKey(key);
    if (metadata[key]?.hidden === true || to_skip.includes(key)) {
      return null;
    }
    const displayType = metadata[key]?.display_type || 'text';

    if (to_skip.includes(key)) {
      return <></>;
    }

    if (label === 'Tech Stack Choices' || label === 'Recommended Tech Stack') {
      return (
        <div key={key} className="my-1 ">
          <Accordion
            title={label}
            content={value}
            preview={`Open this for ${key.toLowerCase()}`}
            renderToHtml={true}
            defaultOpen={true}
          />
        </div>
      );
    }

    if (label === 'Users') {
      return (
        <div key={key} className="my-1 ">
          <Accordion
            title={label}
            content={value}
            preview={`Open this for ${key.toLowerCase()}`}
            renderToHtml={true}
            defaultOpen={true}
          />
        </div>
      );
    }
    if (label === "Title") {
      return (
        <div key={key} className="py-0.5">
          <span className="project-panel-heading ml-2">{value}</span>
        </div>
      );
    }

    if (label === 'Type') {
      return (
        <div key={key} className="py-1">
          <span className="bg-gray-200 rounded-xl px-2 py-0.5 ml-3">{value}</span>
        </div>
      );
    }

    if (label === "Story Points" || label === "User Story Type" || label === "Assigned To") {
      return (
        <div className="flex mb-3 mt-3">
          <strong className="divison-val w-1/6">
            {label}
          </strong>
          <p className="w-4/5">{value}</p>
        </div>
      )
    }
    if (label === "Due Date") {
      return (
        <div className="flex mb-3 mt-3">
          <strong className="divison-val w-1/6">
            {label}
          </strong>
          <p className="w-4/5">{formatUTCToLocal(value) || "N/A"}</p>
        </div>
      )
    }
    if (label === 'Repository Name' || label === 'Root Folder' || label === "Technology") {
      return (
        <div className="flex mb-2 mt-3">
          <span className="project-panel-heading  w-1/6 ml-3">
            {label}
          </span>
          <p className="w-4/5 text-font">{value}</p>
        </div>
      )
    }

    if (label === "Is Architectural Leaf") {
      return (
        <div className="flex mb-2 mt-3">
          <span className="project-panel-heading w-1/6 ml-3">
            {label}
          </span>
          <div className="flex items-center w-4/5">
            <p className="mr-2">{value}</p>
            <div className="w-full ml-4">
              {properties?.IsArchitecturalLeaf?.toLowerCase() === "yes" && (
                <button
                  className="inline-flex items-center px-3 py-1.5 bg-blue-500 text-white typography-body-sm font-weight-medium rounded-full hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                  onClick={() => {
                    router.push(buildProjectUrl(projectId, `architecture/design/${architectureId}`));
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                  Go to design page
                </button>
              )}
            </div>
          </div>
        </div>
      );
    }


    if (label === "Acceptance Criteria") {
      return (
        <div className='-ml-3 -mt-3'>
          <div key={key} className="mb-1 p-1 rounded-lg flex">
            <Accordion
              title={label}
              content={value}
              preview={`Open this for ${key.toLowerCase()}`}
              renderToHtml={true}
              defaultOpen={true}
            />
          </div></div>
      );
    }

    switch (displayType) {
      case 'title':
        return (
          <div key={key} className="py-0.5">
            <span className="project-panel-heading ml-2">{value}</span>
          </div>
        );

      case 'text':
        return (
          <div className="flex mb-2 mt-3">
            <span className="project-panel-heading  w-1/6 ml-3">
              {label}
            </span>
            <p className="w-4/5 text-font">{value}</p>
          </div>
        );
      case 'label':

        return (
          <div key={key} className="py-1">
            <span className="bg-gray-200 rounded-xl  py-0.5">{value}</span>
          </div>
        );
      case 'rich_text':

        return (
          <div className='my-1 '>
            <Accordion
              key={key}
              title={label}
              content={value}
              preview={`Open this for ${key.toLowerCase()}`}
              renderToHtml={true}
              defaultOpen={true}
            /></div>
        )
      case 'mermaid_chart':
        return (
          <div className='my-1  mt-3 '>

            <ChartAccordion key={key} title={label} isopen={true}>
              {value ? (
                <NoSSR chartDefinition={value} />
              ) : (
                <p className="error-message">Error: No chart definition available.</p>
              )}
            </ChartAccordion></div>
        )
      case 'mermaid':
        return (
          <div className='my-1  mt-3 '>

            <ChartAccordion key={key} title={label} isopen={true}>
              {value ? (
                <NoSSR chartDefinition={value} />
              ) : (
                <p className="error-message">Error: No chart definition available.</p>
              )}
            </ChartAccordion></div>
        )
      case 'plantuml':
        return (
          <div className='my-1  mt-3'>

            <ChartAccordion key={key} title={label} isopen={true}>
              {value ? (
                <NoSSR chartDefinition={value} />
              ) : (
                <p className="error-message">Error: No chart definition available.</p>
              )}
            </ChartAccordion></div>
        )

        case 'api_doc':
          return (
            <div className="my-1 mt-3">
              <APIDocumentation apiDetails={value} />
            </div>
          );
      default:

        return (
          <div key={key} className="mb-1 p-1 rounded-lg flex">
            <span className="break-words">
              <b className='text-[#565656] font-weight-semibold  pr-2 -ml-3'>{label}</b>
              <span className='project-panel-content pl-5'> {value}</span>
            </span>
          </div>
        );
    }
  };

  const renderProperties = (properties, metadata) => {
    const keysToRender = to_show.length > 0 ? to_show : Object.keys(metadata);
    return (
      <div className="">
        {keysToRender.map((key) => {

          if (properties[key]) {

            return renderField(key, properties[key], metadata);
          }

          return null;
        })}
      </div>
    );
  };

  return (
    <div>
      {renderProperties(properties, metadata)}
    </div>
  );
};

export default PropertiesRenderer;

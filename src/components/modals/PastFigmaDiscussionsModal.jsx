"use client"
import React from 'react';
import { X } from 'lucide-react';
import TableComponent from '../SimpleTable/pastDiscussionTable';
import EmptyStateView from '../Modal/EmptyStateModal';

function PastFigmaDiscussionsModal({ isOpen, onClose, discussions, isLoading, onRowClick, totalCount, onPageChange, onPageSizeChange }) {
  const headers = [
    {
      key: 'discussion_name',
      label: 'Id',
      render: (value) => (
        <div className="flex items-center">
          <span className="font-weight-medium">{value || 'Untitled Discussion'}</span>
        </div>
      )
    },
    {
      key: 'created_at',
      label: 'Created at',
      render: (value) => value
    },
    // {
    //   key: 'message_count',
    //   label: 'Messages',
    //   render: (value) => (
    //     <span className="rounded-full bg-blue-100 px-2.5 py-1 typography-caption font-weight-medium text-blue-800">
    //       {value} messages
    //     </span>
    //   )
    // },
    {
      key: 'status',
      label: 'Status',
      render: (value) => (
        <span className={`px-2 py-1 typography-caption rounded-full ${
          value === 'COMPLETED' ? 'bg-green-100 text-green-800' :
          value === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {value}
        </span>
      )
    }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-11/12 max-w-6xl max-h-[90vh] flex flex-col">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="typography-heading-4 font-weight-semibold">Past Discussions</h2>
          <button 
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full"
          >
            <X className="w-6 h-6" />
          </button>
        </div>
       {discussions.length>0?(
        <div className="flex-1 overflow-auto p-4">
          <TableComponent
            data={discussions}
            headers={headers}
            onRowClick={(row) => {
              
              if (typeof row === 'string') {
                onRowClick({ id: row });
              } else if (row && row.id) {
                onRowClick(row);
              } else {
                
              }
            }}
            sortableColumns={{ created_at: true, status: true }}
            totalCount={totalCount}
            onPageChange={onPageChange}
            onPageSizeChange={onPageSizeChange}
            component="figma-discussions"
            isLoading={isLoading}
            emptyMessage="No discussions found"
          />
        </div>
        ):(
          <EmptyStateView type='discussions'/>
        )}
      </div>
    </div>
  );
}

export default PastFigmaDiscussionsModal; 
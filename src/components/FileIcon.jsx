import React from "react";
import {
  FaFileAlt,
  FaFileCsv,
  FaFilePdf,
  FaFileWord,
  FaFileExcel,
  FaFilePowerpoint,
  FaFileArchive,
  FaFileImage,
  FaFileVideo,
  FaFileAudio,
  FaJsSquare,
  FaHtml5,
  FaCss3Alt,
  FaMarkdown,
  FaFileCode,
  FaJava,
  FaPhp,
  FaRust,
  FaDatabase,
  FaDocker,
  FaYarn,
  FaNpm,
  FaGitAlt,
  FaSwift,
  FaTerminal,
  FaCog,
} from "react-icons/fa";
import * as SiIcons from "react-icons/si";
import { VscJson } from "react-icons/vsc";
import { BiLogoPython } from "react-icons/bi";

const FileIcon = ({ filename, className = "mr-1" }) => {
  const parts = filename.split(".");
  const extension = parts.length > 1 ? parts.pop().toLowerCase() : "";
  const name = parts.join(".").toLowerCase();

  if (!extension) {
    switch (name) {
      case "dockerfile":
        return <FaDocker className={`text-blue-600 ${className}`} />;
      case "license":
        return <FaFileAlt className={`text-gray-600 ${className}`} />;
      case "readme":
        return <FaMarkdown className={`text-gray-600 ${className}`} />;
      default:
        return <FaFileAlt className={`text-gray-400 ${className}`} />;
    }
  }

  const iconMap = {
    "js": () => <FaJsSquare className={`text-yellow-500 ${className}`} />,
    "jsx": () => <SiIcons.SiReact className={`text-blue-400 ${className}`} />,
    "ts": () => <SiIcons.SiTypescript className={`text-blue-600 ${className}`} />,
    "tsx": () => <SiIcons.SiTypescript className={`text-blue-500 ${className}`} />,
    "py": () => <BiLogoPython className={`text-blue-500 ${className}`} />,
    "java": () => <FaJava className={`text-red-600 ${className}`} />,
    "class": () => <FaJava className={`text-red-600 ${className}`} />,
    "jar": () => <FaJava className={`text-red-600 ${className}`} />,
    "php": () => <FaPhp className={`text-purple-600 ${className}`} />,
    "rb": () => <SiIcons.SiRuby className={`text-red-700 ${className}`} />,
    "rs": () => <FaRust className={`text-primary-700 ${className}`} />,
    "cpp": () => <SiIcons.SiCplusplus className={`text-blue-700 ${className}`} />,
    "c": () => <SiIcons.SiCplusplus className={`text-blue-800 ${className}`} />,
    "cs": () => <SiIcons.SiSharp className={`text-green-600 ${className}`} />,
    "kt": () => <SiIcons.SiKotlin className={`text-primary-500 ${className}`} />,
    "go": () => <SiIcons.SiGo className={`text-blue-500 ${className}`} />,
    "dart": () => <SiIcons.SiDart className={`text-blue-400 ${className}`} />,
    "lua": () => <SiIcons.SiLua className={`text-navy-600 ${className}`} />,
    "scala": () => <SiIcons.SiScala className={`text-red-600 ${className}`} />,
    "ex": () => <SiIcons.SiElixir className={`text-purple-500 ${className}`} />,
    "swift": () => <FaSwift className={`text-primary-500 ${className}`} />,

    // Web Technologies
    "html": () => <FaHtml5 className={`text-primary-600 ${className}`} />,
    "htm": () => <FaHtml5 className={`text-primary-600 ${className}`} />,
    "css": () => <FaCss3Alt className={`text-blue-600 ${className}`} />,
    "scss": () => <SiIcons.SiSass className={`text-pink-600 ${className}`} />,
    "sass": () => <SiIcons.SiSass className={`text-pink-600 ${className}`} />,
    "less": () => <SiIcons.SiLess className={`text-blue-400 ${className}`} />,
    "styl": () => <SiIcons.SiStylus className={`text-green-500 ${className}`} />,
    "vue": () => <SiIcons.SiVuedotjs className={`text-green-500 ${className}`} />,
    "slidev": () => <SiIcons.SiSlidev className={`text-red-500 ${className}`} />,
    "svelte": () => <SiIcons.SiSvelte className={`text-red-500 ${className}`} />,
    "ng": () => <SiIcons.SiAngular className={`text-red-600 ${className}`} />,

    // Data & Config Files
    "json": () => <VscJson className={`text-yellow-700 ${className}`} />,
    "yaml": () => <FaFileCode className={`text-red-400 ${className}`} />,
    "yml": () => <FaFileCode className={`text-red-400 ${className}`} />,
    "xml": () => <FaFileCode className={`text-primary-400 ${className}`} />,
    "csv": () => <FaFileCsv className={`text-green-600 ${className}`} />,
    "graphql": () => <SiIcons.SiGraphql className={`text-pink-600 ${className}`} />,
    "sql": () => <FaDatabase className={`text-blue-400 ${className}`} />,
    "db": () => <FaDatabase className={`text-blue-400 ${className}`} />,

    // Documentation & Text
    "md": () => <FaMarkdown className={`text-gray-600 ${className}`} />,
    "mdx": () => <FaMarkdown className={`text-gray-600 ${className}`} />,
    "txt": () => <FaFileAlt className={`text-gray-500 ${className}`} />,
    "pdf": () => <FaFilePdf className={`text-red-600 ${className}`} />,

    // Microsoft Office
    "doc": () => <FaFileWord className={`text-blue-700 ${className}`} />,
    "docx": () => <FaFileWord className={`text-blue-700 ${className}`} />,
    "xls": () => <FaFileExcel className={`text-green-700 ${className}`} />,
    "xlsx": () => <FaFileExcel className={`text-green-700 ${className}`} />,
    "ppt": () => <FaFilePowerpoint className={`text-red-700 ${className}`} />,
    "pptx": () => <FaFilePowerpoint className={`text-red-700 ${className}`} />,

    // Media Files
    "jpg": () => <FaFileImage className={`text-blue-400 ${className}`} />,
    "jpeg": () => <FaFileImage className={`text-blue-400 ${className}`} />,
    "png": () => <FaFileImage className={`text-blue-400 ${className}`} />,
    "gif": () => <FaFileImage className={`text-blue-400 ${className}`} />,
    "svg": () => <FaFileImage className={`text-blue-400 ${className}`} />,
    "mp4": () => <FaFileVideo className={`text-purple-400 ${className}`} />,
    "mov": () => <FaFileVideo className={`text-purple-400 ${className}`} />,
    "avi": () => <FaFileVideo className={`text-purple-400 ${className}`} />,
    "mp3": () => <FaFileAudio className={`text-green-400 ${className}`} />,
    "wav": () => <FaFileAudio className={`text-green-400 ${className}`} />,

    // Archive Files
    "zip": () => <FaFileArchive className={`text-yellow-600 ${className}`} />,
    "rar": () => <FaFileArchive className={`text-yellow-600 ${className}`} />,
    "7z": () => <FaFileArchive className={`text-yellow-600 ${className}`} />,
    "tar": () => <FaFileArchive className={`text-yellow-600 ${className}`} />,
    "gz": () => <FaFileArchive className={`text-yellow-600 ${className}`} />,

    // Development Tools & Config
    "gitignore": () => <FaGitAlt className={`text-primary-600 ${className}`} />,
    "npmrc": () => <FaNpm className={`text-red-600 ${className}`} />,
    "yarnrc": () => <FaYarn className={`text-blue-400 ${className}`} />,
    "env": () => <FaCog className={`text-gray-500 ${className}`} />,
    "sh": () => <FaTerminal className={`text-gray-600 ${className}`} />,
    "bash": () => <FaTerminal className={`text-gray-600 ${className}`} />,
    "zsh": () => <FaTerminal className={`text-gray-600 ${className}`} />,
    "fish": () => <FaTerminal className={`text-gray-600 ${className}`} />,
    "ps1": () => <FaTerminal className={`text-blue-600 ${className}`} />,

    // Design Files
    "fig": () => <SiIcons.SiFigma className={`text-purple-500 ${className}`} />,
    "sketch": () => <SiIcons.SiSketch className={`text-yellow-500 ${className}`} />,

    // Database Files
    "sqlite": () => <FaDatabase className={`text-blue-500 ${className}`} />,
    "sqlite3": () => <FaDatabase className={`text-blue-500 ${className}`} />,
    "postgres": () => <SiIcons.SiPostgresql className={`text-blue-600 ${className}`} />,
    "mysql": () => <SiIcons.SiMysql className={`text-blue-700 ${className}`} />,
    "mongodb": () => <SiIcons.SiMongodb className={`text-green-500 ${className}`} />,
  };

  // Return the mapped icon or default icon
  return iconMap[extension]?.() || <FaFileAlt className={`text-gray-400 ${className}`} />;
};

export default FileIcon;
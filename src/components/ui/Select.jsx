// components/ui/Select.jsx
import { cn } from '../../lib/utils';

const Select = ({ children, className = '', ...props }) => (
  <select
    className={cn(
      'block w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white',
      className
    )}
    {...props}
  >
    {children}
  </select>
);

export default Select;
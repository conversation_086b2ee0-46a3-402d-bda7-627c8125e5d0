import React, { useState, useEffect, useRef, useContext } from "react";
import { FaUserPlus } from "react-icons/fa";
import UserIcon from "./UserIcon";
import { usePathname } from "next/navigation";
import { AlertContext } from "../NotificationAlertService/AlertList";
import Cookies from "js-cookie";
import {
  listAllUsers,
  getProjectUserToAdd,
  connectUsersToDiscussion,
  getUsersInDiscussion,
} from "@/utils/api";
import AddUsersProject from "../Modal/AddUsersProject";
import UserListView from "../Modal/UserListView";
import { BootstrapTooltip } from "../UIComponents/ToolTip/Tooltip-material-ui";
const UserIcons = () => {
  const [usersInDiscussion, setUsersInDiscussion] = useState([]);
  const [allUsers, setAllUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isUserListOpen, setIsUserListOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [buttonText, setButtonText] = useState("Submit");
  const { showAlert } = useContext(AlertContext);
  const pathName = usePathname();
  const projectId = pathName.split("/")[3];
  const loggedInUserId = Cookies.get("userId");
  const modalRef = useRef(null);

  const fetchAllUsers = async () => {
    try {
      const [allUsersResponse, usersInDiscussionResponse] = await Promise.all([
        listAllUsers(),
        getUsersInDiscussion(projectId),
      ]);

      setAllUsers(allUsersResponse.users);
      setUsersInDiscussion(usersInDiscussionResponse.members);

      await updateFilteredUsers();
    } catch (error) {
      
    } finally {
      setIsLoading(false);
    }
  };

  const updateFilteredUsers = async () => {
    try {
      const response = await getProjectUserToAdd(projectId);
      const filtered = response.members.filter(
        (user) => user.Username !== loggedInUserId
      );
      setFilteredUsers(filtered);
    } catch (error) {
      
    }
  };

  useEffect(() => {
    fetchAllUsers();
  }, [projectId, loggedInUserId]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
        setIsUserListOpen(false);
      }
    };

    if (isDropdownOpen || isUserListOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDropdownOpen, isUserListOpen]);

  const handleSubmit = async (e, members = []) => {
    if (selectedUsers.includes(loggedInUserId)) {
      showAlert("You cannot add yourself to the project.", "danger");
      setButtonText("Submit");
      return;
    }

    setButtonText("Submitting...");

    if (!members.length) {
      members = selectedUsers.map((userId) => ({
        user_id: userId,
        role: "Developer",
        responsibilities: [],
      }));
    }

    try {
      const response = await connectUsersToDiscussion(projectId, members);
      if (
        response.message === `Project ${projectId} members updated successfully`
      ) {
        await updateFilteredUsers(); // Only update filtered users
        await getUsersInDiscussion(projectId).then((res) =>
          setUsersInDiscussion(res.members)
        ); // Update users in discussion
        setSelectedUsers([]);
        setIsDropdownOpen(false);
        showAlert("Users successfully added to Project!", "success");
      }
    } catch (error) {
      showAlert("Error adding users to discussion:", "danger");
      
    } finally {
      setButtonText("Submit");
    }
  };

  const getInitials = (name) => {
    if (!name) return "";
    const nameParts = name.split(" ");
    return nameParts.length === 1
      ? nameParts[0].slice(0, 2).toUpperCase()
      : nameParts[0][0].toUpperCase() + nameParts[nameParts.length - 1][0].toUpperCase();
  };

  return (
    <div>
      <div className="flex items-center">
        <div className="flex items-center -space-x-1.5">
          {usersInDiscussion.slice(0, 3).map((user, index) => (
            <div key={user.user_id} className="group relative">
              <UserIcon
                initials={getInitials(user.name || user.email)}
                bgColor={index % 2 === 0 ? "blue" : "green"}
                email={user.email}
                width={32}
                height={32}
              />
            </div>
          ))}
          <div className="relative">
          <BootstrapTooltip title="Add users to project" placement="bottom">
            <span>
              <FaUserPlus
                className="text-[#2472eb] p-1 border bg-[#dfeafc] rounded-full cursor-pointer"
                size={36}
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              />
            </span>
          </BootstrapTooltip>
          </div>
        </div>
      </div>
      {isDropdownOpen && (
        <>
          {isLoading ? (
            <p className="text-white text-center">Loading...</p>
          ) : (
            <AddUsersProject
              filteredUsers={filteredUsers}
              handleSubmit={handleSubmit}
              setFilteredUsers={setFilteredUsers}
              isOpen={isDropdownOpen}
              setIsOpen={setIsDropdownOpen}
              usersInDiscussion={usersInDiscussion}
            />
          )}
        </>
      )}
      {isUserListOpen && (
        <UserListView
          usersInDiscussion={usersInDiscussion}
          setIsUserListOpen={setIsUserListOpen}
        />
      )}
    </div>
  );
};

export default UserIcons;

// FileAttachment.js
import React from 'react';

const FileAttachment = ({ attachment, onView }) => (
  <div className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-gray-50 transition-colors duration-150 ease-in-out">
    <div className="flex items-center space-x-3 min-w-0 flex-1">
      <svg className="flex-shrink-0 w-5 h-5 text-gray-400" fill="none" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" viewBox="0 0 24 24" stroke="currentColor">
        <path d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
      </svg>
      <span className="typography-body-sm font-weight-medium text-gray-700 truncate max-w-[200px]" title={attachment.file_name}>
        {attachment.file_name.length > 60 
          ? attachment.file_name.substring(0, 60) + '...' 
          : attachment.file_name}
      </span>
    </div>
    <button 
      onClick={() => onView(attachment)} 
      className="flex-shrink-0 px-3 py-1 ml-2 typography-body-sm font-weight-medium text-blue-600 bg-blue-100 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-150 ease-in-out"
    >
      View
    </button>
  </div>
);

export default FileAttachment;
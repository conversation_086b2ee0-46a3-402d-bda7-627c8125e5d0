import React, { useState, useMemo } from 'react';
import { Activity, AlertTriangle, ChevronDown, ChevronUp, CodeXml, Download, Edit, Flame, LeafyGreen, LucideIcon, Siren, Trash2 } from 'lucide-react';
import Pagination from '../UIComponents/Paginations/Pagination';
import { DynamicButton } from '../UIComponents/Buttons/DynamicButton';

type Status = 'SUCCESS' | 'FAILED' | 'RUNNING' | string;
type Priority = 'Critical' | 'High' | 'Medium' | 'Low';

interface StatusBadgeProps {
  status: Status;
}

interface PriorityBadgeProps {
  priority: string | number | undefined;
  isDropdown?: boolean;
}

interface HeaderProps {
  key: string;
  label: string;
  actionLabel?: string;
  onClick?: (e: React.MouseEvent<HTMLAnchorElement>, orgId: string) => void;
}

interface SortConfig {
  key: string | null;
  direction: 'ascending' | 'descending';
}

interface PriorityConfig {
  icon: LucideIcon;
  styles: string;
  iconColor: string;
  animation?: string;
  pulse?: boolean;
}

interface TableComponentProps {
  headers: HeaderProps[];
  data: Array<{ [key: string]: any }>;
  onRowClick: (id: string, type?: string) => void;
  sortableColumns?: { [key: string]: boolean };
  itemsPerPage?: number;
  onActionClick?: (id: string | number) => void;
  title?: string;
  type?: string; // Add this line
  onEdit?: (row: any) => void; // Add this line
  onDownload?: (row: any) => void; // Add this line
  onDelete?: (row: any) => void; // Add this line
  onCheckboxChange?: (id: string, checked: boolean, row: any) => void;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {

  const greenBadge = { bgColor: 'bg-green-100', textColor: 'text-green-800', dotColor: 'bg-green-500' };
  const redBadge = { bgColor: 'bg-red-100', textColor: 'text-red-800', dotColor: 'bg-red-500' };
  const blueBadge = { bgColor: 'bg-primary-100', textColor: 'text-primary-800', dotColor: 'bg-primary-500' };
  const yellowBadge = { bgColor: 'bg-yellow-100', textColor: 'text-yellow-800', dotColor: 'bg-yellow-500' };

  const config = {
    SUCCESS: greenBadge,
    Active: greenBadge,
    verified:greenBadge,
    pending :yellowBadge,
    Running :greenBadge,
    active: greenBadge,
    FAILED: redBadge,
    Inactive: redBadge,
    inactive: redBadge,
    RUNNING: blueBadge,
    Suspended: blueBadge,
    suspended: blueBadge
  }[status] || { bgColor: 'bg-red-100', dotColor: 'bg-red-500', textColor: 'text-red-800' };

  return (
    <div className={`inline-flex items-center px-1.5 sm:px-2 py-0.5 rounded-full ${config.bgColor} min-w-0 max-w-full`}>
      <div className={`w-1 h-1 rounded-full mr-1 flex-shrink-0 ${config.dotColor}`} />
      <span className={`typography-caption font-weight-medium capitalize ${config.textColor} truncate`}>
        {status.toLowerCase()}
      </span>
    </div>
  );
};

export const PriorityBadge: React.FC<PriorityBadgeProps> = ({ priority, isDropdown = false }) => {
  const normalizedPriority = (() => {
    if (typeof priority === "number") return 'Low';
    return priority;
  })();

  const priorityConfig: Record<Priority | 'default', PriorityConfig> = {
    'Critical': {
      icon: Siren,
      styles: 'bg-red-100 text-red-900 border-red-300 shadow-md ring-2 ring-red-400 ring-opacity-50',
      iconColor: 'text-red-700',
      pulse: true
    },
    'High': {
      icon: Flame,
      styles: 'bg-red-100 text-red-800 border-red-200 shadow-sm',
      iconColor: 'text-red-600',
      pulse: true
    },
    'Medium': {
      icon: Activity,
      styles: 'bg-yellow-100 text-yellow-800 border-yellow-200 shadow-sm',
      iconColor: 'text-yellow-600',
      animation: 'hover:scale-105 transition-transform duration-200'
    },
    'Low': {
      icon: LeafyGreen,
      styles: 'bg-green-100 text-green-800 border-green-200 shadow-sm',
      iconColor: 'text-green-600'
    },
    'default': {
      icon: AlertTriangle,
      styles: 'bg-gray-100 text-gray-600 border-gray-200 shadow-sm',
      iconColor: 'text-gray-500'
    }
  };

  const config = priorityConfig[normalizedPriority as Priority] || priorityConfig.default;
  const Icon = config.icon;

  return (
    <span
      className={`
      inline-flex items-center px-2 py-1 typography-caption font-weight-medium
      rounded-full border whitespace-nowrap ${config.styles} ${config.pulse ? 'animate-pulse' : ''}
      ${config.animation || ''} cursor-default
    `}
    >
      <Icon className={`w-3 h-3 mr-1.5 ${config.iconColor}`} />
      {normalizedPriority || 'Not Set'}
      {isDropdown && <ChevronDown className="w-3 h-3 ml-1" />}
    </span>
  );
};

const TableComponent: React.FC<TableComponentProps> = ({
  headers,
  data = [],
  onRowClick,
  sortableColumns = {},
  itemsPerPage = 20,
  onActionClick,
  title,
  type,
  onEdit,
  onDownload,
  onDelete,
  onCheckboxChange
}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: 'ascending' });
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);
  const [checkboxStates, setCheckboxStates] = useState<Record<string, boolean>>({});

  const sortedData = useMemo(() => {
    if (!sortConfig.key) return data;
    return [...data].sort((a, b) => {
      const aVal = a[sortConfig.key as string];
      const bVal = b[sortConfig.key as string];

      // Handle null/undefined values
      if (aVal == null && bVal == null) return 0;
      if (aVal == null) return sortConfig.direction === 'ascending' ? 1 : -1;
      if (bVal == null) return sortConfig.direction === 'ascending' ? -1 : 1;

      // Handle numeric values (for credits, cost, etc.)
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return sortConfig.direction === 'ascending' ? aVal - bVal : bVal - aVal;
      }

      // Handle string values (case-insensitive)
      const aStr = String(aVal).toLowerCase();
      const bStr = String(bVal).toLowerCase();

      if (aStr < bStr) return sortConfig.direction === 'ascending' ? -1 : 1;
      if (aStr > bStr) return sortConfig.direction === 'ascending' ? 1 : -1;
      return 0;
    });
  }, [data, sortConfig]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return sortedData.slice(startIndex, startIndex + pageSize);
  }, [sortedData, currentPage, pageSize]);

  const totalItems = sortedData.length;
  const pageCount = Math.ceil(totalItems / pageSize);

  // Helper function to get column minimum width - more compact for better space utilization
  const getColumnMinWidth = (key: string) => {
    const widthMap: { [key: string]: string } = {
      'name': '100px',
      'email': '130px',
      'role': '60px',
      'current_plan_name': '85px',
      'allocatedCredits': '75px',
      'remainingCredits': '75px',
      'cost': '65px',
      'status': '65px',
      'title': title === 'System Containers' ? '200px' : '140px',
      'assignee': '120px',
      'menuActions': '30px'
    };
    return widthMap[key] || 'auto';
  };

  const TableHeader: React.FC<{ header: HeaderProps }> = ({ header }) => (
    <th
      key={header.key}
      onClick={() => sortableColumns[header.key] && requestSort(header.key)}
      className={`px-3 py-2 typography-caption font-weight-bold text-[#687182] uppercase ${['id', 'action', 'status', 'type', 'menuActions'].includes(header.key) ? 'text-center' : 'text-left'} ${sortableColumns[header.key] ? 'cursor-pointer hover:bg-gray-50' : ''
        }`}
      style={{ minWidth: getColumnMinWidth(header.key) }}
    >
      <div className={`flex items-center space-x-1 ${['id', 'action', 'status', 'type', 'menuActions'].includes(header.key) ? 'justify-center' : 'justify-start'}`}>
        <span className='text-nowrap'>{header.label}</span>
        {sortableColumns[header.key] && (
          <div className="flex flex-col professional-table-sort-icon">
            <ChevronUp
              className={`h-3 w-3 ${sortConfig.key === header.key && sortConfig.direction === 'ascending'
                ? 'text-orange-600'
                : 'text-[#A1A9B8]'
                }`}
            />
            <ChevronDown
              className={`h-3 w-3 ${sortConfig.key === header.key && sortConfig.direction === 'descending'
                ? 'text-orange-600'
                : 'text-[#A1A9B8]'
                }`}
            />
          </div>
        )}
      </div>
    </th>
  );

  const requestSort = (key: string) => {
    if (!sortableColumns[key]) return;
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'ascending' ? 'descending' : 'ascending'
    }));
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>, id: string, row: any) => {
    e.stopPropagation();
    const isChecked = e.target.checked;

    // If checked, set only the current id to true and others to false
    setCheckboxStates(prevState => {
      const newState: Record<string, boolean> = {};

      // Only one checkbox can be checked at a time
      Object.keys(prevState).forEach(key => {
        newState[key] = false;
      });

      if (isChecked) {
        newState[id] = true;
      }

      return newState;
    });

    if (onCheckboxChange) {
      onCheckboxChange(id, isChecked, row);
    }
  };


  return (
    <div className="w-full bg-white border border-[#E9EDF5] rounded-lg shadow">
      {
        title && (
          <div className="bg-gray-100 px-4 py-2 font-weight-medium text-gray-700">
            {title}
          </div>
        )
      }
      <div className="overflow-x-auto w-full">
        <table className="w-full border-collapse divide-y divide-[#E9EDF5]" style={{ minWidth: '750px' }}>
          <thead className="professional-table-header">
            <tr>
              {headers.map((header) => (
                <TableHeader key={header.key} header={header} />
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-[#E9EDF5]">
            {paginatedData.map((row, index) => (
              <tr
                key={row.id || index}
                onClick={() => !row.isDisabled && onRowClick(row.id, row.type)}
                className={`professional-table-row transition-colors ${row.rowClassName || ''} ${row.isDisabled ? 'cursor-not-allowed hover:bg-gray-50' : 'cursor-pointer'}`}
              >
                {headers.map((header) => (
                  <td
                    key={header.key}
                    className={`px-3 py-2 ${['id', 'action', 'status', 'type', 'menuActions'].includes(header.key) ? 'text-center' : 'text-left'} ${header.key === 'Priority' ? 'text-center' : ''}`}
                    style={{
                      minWidth: getColumnMinWidth(header.key)
                    }}
                  >
                    {header.key === 'status' ? (
                      <StatusBadge status={row[header.key]} />
                    ) : header.key === 'action' ? (
                      type === 'image' ? (
                        <div className="flex justify-center gap-6"> {/* Changed gap-4 to gap-6 */}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onEdit?.(row);
                            }}
                            className="text-gray-600 hover:text-blue-600 transition-colors"
                            title="Rename"
                          >
                            <Edit size={18} />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onDownload?.(row);
                            }}
                            className="text-gray-600 hover:text-green-600 transition-colors"
                            title="Download"
                          >
                            <Download size={18} />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onDelete?.(row);
                            }}
                            className="text-gray-600 hover:text-red-600 transition-colors"
                            title="Delete"
                          >
                            <Trash2 size={18} />
                          </button>
                        </div>
                      ) : (type === 'pod' || type === 'available-pod')? (
                        <div className="flex justify-center gap-6">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onDelete?.(row);
                            }}
                            className="text-red-500 hover:text-red-700 p-1 transition-colors"
                            title={type === 'available-pod' ? "Delete Available Pod" : "Delete Pod"}
                          >
                            <Trash2 size={18} />
                          </button>
                        </div>
                      ) : (
                        <DynamicButton
                          type="submit"
                          variant="primaryOutline"
                          icon={CodeXml}
                          onClick={(e) => {
                            e.stopPropagation();
                            onActionClick?.(row.id);
                          }}
                          text={header.actionLabel || 'Action'}
                        />
                      )
                    ) : header.key === 'type' ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full typography-caption font-weight-medium bg-gray-100 text-[#2A3439]">
                        {row[header.key]}
                      </span>
                    ) : header.key === 'container_type' ? (
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full typography-caption font-weight-medium ${row[header.key]?.toLowerCase() === 'internal'
                        ? 'bg-orange-100 text-orange-800'
                        : row[header.key]?.toLowerCase() === 'external'
                          ? 'bg-gray-100 text-[#2A3439]'
                          : 'bg-gray-100 text-[#2A3439]'
                        }`}>
                        {row[header.key]}
                      </span>
                    ) : header.key === 'isProjectGuidance-checkbox' ? (
                      <div onClick={e => e.stopPropagation()} className="flex justify-center">
                        <input
                          type="checkbox"
                          checked={checkboxStates[row.id] ?? row[header.key] ?? false}
                          onChange={(e) => handleCheckboxChange(e, row.id, row)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer disabled:pointer-events-none disabled:opacity-50 disabled:cursor-not-allowed"
                          disabled={row.container_type?.toLowerCase() === 'external'}
                        />
                      </div>
                    )
                      : header.key === 'Priority' ? (
                        <PriorityBadge priority={row[header.key]} />
                      ) : header.key === 'remainingCredits' ? (
                        <div className={`font-weight-medium ${row[header.key] == 0 ? 'text-red-500' : 'text-gray-900'}`}>
                          {row[header.key]}
                        </div>
                      ) : header.key === 'allocatedCredits' ? (
                        <div className="font-weight-medium text-gray-900">
                          {row[header.key]}
                        </div>
                      ) : header.key === 'cost' && header.onClick ? (
                        <a
                          className='hover:text-primary-600 font-weight-medium text-gray-900 cursor-pointer transition-colors'
                          onClick={(e) => header.onClick && header.onClick(e, paginatedData[index].id)}
                        >
                          {row[header.key]}
                        </a>
                      ) : (
                        <div
                          className={`table-compact-text text-[#2A3439] ${['email', 'name'].includes(header.key)
                              ? 'multiline'
                              : header.key === 'description'
                                ? 'truncate max-w-xs'
                                : header.key === 'current_plan_name'
                                  ? ''
                                  : ''
                            } ${["id"].includes(header.key.toLowerCase()) ? "text-blue-600 underline" : ""}`}
                          title={
                            ['email', 'name', 'description', 'current_plan_name'].includes(header.key.toLowerCase()) &&
                              row[header.key] &&
                              String(row[header.key]).length > 20
                              ? row[header.key]
                              : undefined
                          }
                        >
                          {row[header.key]}
                        </div>
                      )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <Pagination
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />
    </div>
  );
};

export default TableComponent;
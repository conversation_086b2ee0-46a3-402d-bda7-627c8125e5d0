import React, { useState, useMemo ,useCallback,useContext } from 'react';
import { Activity, AlertTriangle, ChevronDown, ChevronUp, CodeXml, Flame, LeafyGreen, LucideIcon, Siren ,MoreVertical} from 'lucide-react';
import Pagination from '../UIComponents/Paginations/Pagination';
import { DynamicButton } from '../UIComponents/Buttons/DynamicButton';
import {deleteNodeById} from "@/utils/api"
import DeleteProjectModal from "@/components/Modal/DeleteProjectModal";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

type Status = 'SUCCESS' | 'FAILED' | 'RUNNING' | string;
type Priority = 'Critical' | 'High' | 'Medium' | 'Low';

interface StatusBadgeProps {
  status: Status;
}

interface PriorityBadgeProps {
  priority: string | number | undefined;
  isDropdown?: boolean;
}

interface HeaderProps {
  key: string;
  label: string;
  actionLabel?: string;
}

interface SortConfig {
  key: string | null;
  direction: 'ascending' | 'descending';
}

interface PriorityConfig {
  icon: LucideIcon;
  styles: string;
  iconColor: string;
  animation?: string;
  pulse?: boolean;
}

interface TableComponentProps {
  headers: HeaderProps[];
  data: Array<{ [key: string]: any }>;
  onRowClick: (id: string, type?: string) => void;
  sortableColumns?: { [key: string]: boolean };
  onActionClick?: (id: string | number) => void;
  title?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const config = {
    SUCCESS: { bgColor: 'bg-green-100', textColor: 'text-green-800', dotColor: 'bg-green-500' },
    Active: { bgColor: 'bg-green-100', textColor: 'text-green-800', dotColor: 'bg-green-500' },
    FAILED: { bgColor: 'bg-red-100', textColor: 'text-red-800', dotColor: 'bg-red-500' },
    Inactive: { bgColor: 'bg-red-100', textColor: 'text-red-800', dotColor: 'bg-red-500' },
    RUNNING: { bgColor: 'bg-primary-100', textColor: 'text-primary-800', dotColor: 'bg-primary-500' },
    Suspended: { bgColor: 'bg-primary-100', textColor: 'text-primary-800', dotColor: 'bg-primary-500' }
  }[status] || { bgColor: 'bg-red-100', dotColor: 'bg-red-500', textColor: 'text-red-800' };

  return (
    <div className={`inline-flex items-center px-1.5 sm:px-2.5 py-1 rounded-full ${config.bgColor} min-w-0 max-w-full`}>
      <div className={`w-1.5 h-1.5 rounded-full mr-1.5 flex-shrink-0 ${config.dotColor}`} />
      <span className={`typography-caption font-weight-medium capitalize ${config.textColor} truncate`}>
        {status.toLowerCase()}
      </span>
    </div>
  );
};

export const PriorityBadge: React.FC<PriorityBadgeProps> = ({ priority, isDropdown = false }) => {
  const normalizedPriority = (() => {
    if (typeof priority === "number") return 'Low';
    return priority;
  })();

  const priorityConfig: Record<Priority | 'default', PriorityConfig> = {
    'Critical': {
      icon: Siren,
      styles: 'bg-red-100 text-red-900 border-red-300 shadow-md ring-2 ring-red-400 ring-opacity-50',
      iconColor: 'text-red-700',
      pulse: true
    },
    'High': {
      icon: Flame,
      styles: 'bg-red-100 text-red-800 border-red-200 shadow-sm',
      iconColor: 'text-red-600',
      pulse: true
    },
    'Medium': {
      icon: Activity,
      styles: 'bg-yellow-100 text-yellow-800 border-yellow-200 shadow-sm',
      iconColor: 'text-yellow-600',
      animation: 'hover:scale-105 transition-transform duration-200'
    },
    'Low': {
      icon: LeafyGreen,
      styles: 'bg-green-100 text-green-800 border-green-200 shadow-sm',
      iconColor: 'text-green-600'
    },
    'default': {
      icon: AlertTriangle,
      styles: 'bg-gray-100 text-gray-600 border-gray-200 shadow-sm',
      iconColor: 'text-gray-500'
    }
  };

  const config = priorityConfig[normalizedPriority as Priority] || priorityConfig.default;
  const Icon = config.icon;

  return (
    <span
      className={`
      inline-flex items-center px-2 py-1 typography-caption font-weight-medium
      rounded-full border whitespace-nowrap ${config.styles} ${config.pulse ? 'animate-pulse' : ''}
      ${config.animation || ''} cursor-default
    `}
    >
      <Icon className={`w-3 h-3 mr-1.5 ${config.iconColor}`} />
      {normalizedPriority || 'Not Set'}
      {isDropdown && <ChevronDown className="w-3 h-3 ml-1" />}
    </span>
  );
};

const TableComponent: React.FC<TableComponentProps> = ({
  headers,
  data: initialData = [],
  onRowClick,
  sortableColumns = {},
  onActionClick,
  title,
}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: 'ascending' });
  const [data, setData] = useState(initialData);
  const [dropdownOpen, setDropdownOpen] = useState<{ [key: string]: boolean }>({});
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [discussionId, setDiscussionId] = useState<any>()
  const { showAlert } = useContext(AlertContext);


  const handleDelete =  useCallback(async(id: string | number) => {
    const node_type = "Discussion";
    setIsDeleting(true);
    try {
        const response = await deleteNodeById(id, node_type);
        if (response.status >= 200 && response.status < 300) {
            showAlert("Discussion deleted successfully", "success");
            const updatedData = data.filter(item => item.id !== id);
            setData(updatedData);

    // Reset dropdown state
    const newDropdownState = { ...dropdownOpen };
    delete newDropdownState[id];
    setDropdownOpen(newDropdownState);

    // If current page becomes empty, go to previous page
    const newPageCount = Math.ceil(updatedData.length / pageSize);
    if (currentPage > newPageCount) {
      setCurrentPage(Math.max(1, newPageCount));
    }}}catch (error) {

        showAlert("Failed to delete the Discussion!", "danger");
      } finally {
        setIsDeleting(false);
        setIsDeleteModalOpen(false);
      }
  }, [data, dropdownOpen, pageSize, currentPage]);

  const confirmAndDelete = (id: string | number) => {
    setIsDeleteModalOpen(true);
    setDiscussionId(id)
  };

  const cancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  const proceedWithDelete = async () => {
    await handleDelete(discussionId);
  };


  const sortedData = useMemo(() => {
    if (!sortConfig.key) return data;
    return [...data].sort((a, b) => {
      const aVal = a[sortConfig.key as string];
      const bVal = b[sortConfig.key as string];
      if (aVal < bVal) return sortConfig.direction === 'ascending' ? -1 : 1;
      if (aVal > bVal) return sortConfig.direction === 'ascending' ? 1 : -1;
      return 0;
    });
  }, [data, sortConfig]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return sortedData.slice(startIndex, startIndex + pageSize);
  }, [sortedData, currentPage, pageSize]);

  const totalItems = sortedData.length;
  const pageCount = Math.ceil(totalItems / pageSize);

  const TableHeader: React.FC<{ header: HeaderProps }> = ({ header }) => (
    <th
      key={header.key}
      onClick={() => sortableColumns[header.key] && requestSort(header.key)}
      className={`px-6 py-2.5 typography-heading-6 text-[#687182] uppercase ${['id', 'action', 'status', 'type'].includes(header.key) ? 'text-center' : ''} ${sortableColumns[header.key] ? 'cursor-pointer hover:bg-gray-50' : ''
        }`}
      style={{ minWidth: header.key.toLowerCase() === 'title' ? '200px' : header.key.toLowerCase() === 'id' ? '180px': header.key.toLowerCase() === 'timestamp'? '170px' :'auto' }}
    >
      <div className={`flex items-center space-x-1 ${['id', 'action', 'status', 'type'].includes(header.key) ? 'justify-center' : ''}`}>
        <span>{header.label}</span>
        {sortableColumns[header.key] && (
          <div className="flex flex-col">
            <ChevronUp
              className={`h-3 w-3 ${sortConfig.key === header.key && sortConfig.direction === 'ascending'
                ? 'text-blue-600'
                : 'text-[#A1A9B8]'
                }`}
            />
            <ChevronDown
              className={`h-3 w-3 ${sortConfig.key === header.key && sortConfig.direction === 'descending'
                ? 'text-blue-600'
                : 'text-[#A1A9B8]'
                }`}
            />
          </div>
        )}
      </div>
    </th>
  );

  const requestSort = (key: string) => {
    if (!sortableColumns[key]) return;
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'ascending' ? 'descending' : 'ascending'
    }));
  };


  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };
  return (
    <div className="w-full bg-white border border-[#E9EDF5] rounded-lg shadow">
      {
        title && (
          <div className="bg-gray-100 px-4 py-2 font-weight-medium text-gray-700">
            {title}
          </div>
        )
      }
      <div className="overflow-x-auto w-full">
        <table className="w-full border-collapse divide-y divide-[#E9EDF5]">
          <thead className="bg-[#F7F9FCCC]">
            <tr>
              {headers.map((header) => (
                <TableHeader key={header.key} header={header} />
              ))}
              <th className="px-6 py-2.5 typography-heading-6 text-[#687182] uppercase"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-[#E9EDF5]">
            {paginatedData.map((row, index) => (
              <tr
                key={row.id || index}
                onClick={() => onRowClick(row.id, row.type)}
                className="hover:bg-gray-50 cursor-pointer transition-colors"
              >
                {headers.map((header) => (
                  <td
                    key={header.key}
                    className={`px-6 py-2.5 ${['id', 'action', 'status', 'type'].includes(header.key) ? 'text-center' : ''} ${header.key === 'Priority' ? 'text-center' : ''}`}
                    style={{
                      minWidth: header.key.toLowerCase() === 'title' ? title === 'System Containers'
                        ? '320px'
                        : '230px' : header.key.toLowerCase() === 'assignee' ? '230px' : header.key.toLowerCase() === 'description' ? '200px' : 'auto'
                    }}
                  >
                    {header.key === 'status' ? (
                      <StatusBadge status={row[header.key]} />
                    ) : header.key === 'action' ? (
                      <DynamicButton
                        type="submit"
                        variant="primaryOutline"
                        icon={CodeXml}
                        onClick={(e) => {
                          e.stopPropagation();
                          onActionClick?.(row.id);
                        }}
                        text={header.actionLabel || 'Action'}
                      />
                    ) : header.key === 'type' ? (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full typography-caption font-weight-medium bg-gray-100 text-[#2A3439]">
                        {row[header.key]}
                      </span>
                    ) : header.key === 'Priority' ? (
                      <PriorityBadge priority={row[header.key]} />
                    ) :
                      (
                        <div className={`typography-body-sm text-[#2A3439] custom-table-text-ellipsis ${header.key.toLowerCase() === "title" ? "hover:text-blue-600" : ""
                          }`}
                          title={header.key.toLowerCase() === "description" ? row[header.key] : undefined}>
                          {row[header.key]}
                        </div>
                      )}
                  </td>
                ))}
 {(title === 'Past Discussion' || title === 'Past Tasks')&& (
                  <td className="text-right px-4 py-2">
                    <div className="relative">
                      <button
                        className="text-gray-500 hover:text-gray-700"
                        onClick={(e) => {
                          e.stopPropagation();
                          setDropdownOpen((prev) => ({ ...prev, [row.id]: !prev[row.id] }));
                        }}
                      >
                        <MoreVertical className="w-5 h-5" />
                      </button>
                      {dropdownOpen[row.id] && (
                        <div className={`absolute right-0 w-32 bg-white border rounded shadow-lg z-10 bottom-full mb-1`}>
                          <button
                            className="block w-full text-left px-4 py-2 typography-body-sm text-red-700 hover:bg-red-50"
                            onClick={(e) => {
                              e.stopPropagation();
                              confirmAndDelete(row.id);
                            }}
                          >
                            Delete
                          </button>
                        </div>
                      )}
                    </div>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <Pagination
        currentPage={currentPage}
        pageCount={pageCount}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />
      {isDeleteModalOpen && (
        <DeleteProjectModal
          isOpen={isDeleteModalOpen}
          onClose={cancelDelete}
          onDelete={proceedWithDelete}
          isDeleting={isDeleting}
          type="discussion"
        />
      )}
    </div>
  );
};

export default TableComponent;
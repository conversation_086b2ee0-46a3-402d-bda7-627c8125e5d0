import React, { useState, useEffect } from 'react';
import { Pencil } from 'lucide-react';
import { PulseAnimateLoader } from "@/components/UIComponents/Loaders/LoaderGroup";
import { capitalizeText } from '@/utils/textFormatting';
interface EditableCellProps {
  value: string;
  field: string;
  rowId: string | number;
  onUpdate: (rowId: string | number, field: string, value: string) => Promise<void>;
  onError?: (error: any) => void;
  onSuccess?: (message: string) => void;
}

const EditableCell = ({ value, field, rowId, onUpdate, onError, onSuccess }: EditableCellProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setEditValue(value);
  }, [value]);

  const handleSave = async () => {
    if (editValue === value) {
      setIsEditing(false);
      return;
    }

    setIsLoading(true);
    try {
      await onUpdate(rowId, field, editValue);
      onSuccess?.(`${capitalizeText(field)} updated successfully`);
      setIsEditing(false);
    } catch (error) {
      onError?.(error);
      setEditValue(value);
    } finally {
      setIsLoading(false);
    }
  };

  if (isEditing) {
    return (
      <div className="flex items-center gap-2 w-full">
        <input
          type="text"
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onBlur={handleSave}
          onKeyDown={(e:any) => {
            if (e.key === 'Enter') {
              e.target.blur();
            } else if (e.key === 'Escape') {
              setEditValue(value);
              setIsEditing(false);
            }
          }}
          className="w-full p-1 border border-blue-500 rounded"
          autoFocus
          onClick={e => e.stopPropagation()}
        />
        {isLoading && <PulseAnimateLoader />}
      </div>
    );
  }

  return (
    <div className="group flex items-center justify-between min-w-0 w-full gap-2">
      <div className="truncate flex-1" title={value || '-'}>
        {value || '-'}
      </div>
      <button
        onClick={(e) => {
          e.stopPropagation();
          setIsEditing(true);
        }}
        className="flex-shrink-0 opacity-0 group-hover:opacity-100 text-gray-400 hover:text-blue-600 transition-opacity"
      >
        <Pencil size={14} />
      </button>
    </div>
  );
};

export default EditableCell;
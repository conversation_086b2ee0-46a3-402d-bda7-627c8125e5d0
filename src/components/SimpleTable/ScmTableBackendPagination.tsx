import React, { useState } from 'react';
import { Activity, AlertTriangle, ChevronDown, ChevronUp, CodeXml, Flame, LeafyGreen, LucideIcon, Siren, Gith<PERSON> } from 'lucide-react';
import { DynamicButton } from '../UIComponents/Buttons/DynamicButton';

type Status = 'SUCCESS' | 'FAILED' | 'RUNNING' | string;
type Priority = 'Critical' | 'High' | 'Medium' | 'Low';

interface StatusBadgeProps {
  status: Status;
}

interface PriorityBadgeProps {
  priority: string | number | undefined;
  isDropdown?: boolean;
}

interface HeaderProps {
  key: string;
  label: string;
  actionLabel?: string;
}

interface SortConfig {
  key: string | null;
  direction: 'ascending' | 'descending';
}

interface PriorityConfig {
  icon: LucideIcon;
  styles: string;
  iconColor: string;
  animation?: string;
  pulse?: boolean;
}

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems?: number;
  perPage: number;
  onPageChange: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
}

interface ScmTableProps {
  headers: HeaderProps[];
  data: Array<{ [key: string]: any }>;
  onRowClick: (id: string, type?: string) => void;
  sortableColumns?: { [key: string]: boolean };
  onActionClick?: (id: string | number) => void;
  title?: string;
  pagination?: PaginationProps;
  onSortChange?: (key: string, direction: 'ascending' | 'descending') => void;
  isLoading?: boolean;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const greenBadge = { bgColor: 'bg-green-100', textColor: 'text-green-800', dotColor: 'bg-green-500' };
  const redBadge = { bgColor: 'bg-red-100', textColor: 'text-red-800', dotColor: 'bg-red-500' };
  const blueBadge = { bgColor: 'bg-orange-100', textColor: 'text-orange-800', dotColor: 'bg-orange-500' };
  const yellowBadge = { bgColor: 'bg-yellow-100', textColor: 'text-yellow-800', dotColor: 'bg-yellow-500' };

  const config = {
    SUCCESS: greenBadge,
    Active: greenBadge,
    active: greenBadge,
    FAILED: redBadge,
    Inactive: redBadge,
    inactive: redBadge,
    RUNNING: blueBadge,
    Suspended: blueBadge,
    suspended: blueBadge
  }[status] || { bgColor: 'bg-red-100', dotColor: 'bg-red-500', textColor: 'text-red-800' };

  return (
    <div className={`inline-flex items-center px-2.5 py-1 rounded-full ${config.bgColor}`}>
      <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${config.dotColor}`} />
      <span className={`typography-caption font-weight-medium capitalize ${config.textColor}`}>
        {status.toLowerCase()}
      </span>
    </div>
  );
};

export const PriorityBadge: React.FC<PriorityBadgeProps> = ({ priority, isDropdown = false }) => {
  const normalizedPriority = (() => {
    if (typeof priority === "number") return 'Low';
    return priority;
  })();

  const priorityConfig: Record<Priority | 'default', PriorityConfig> = {
    'Critical': {
      icon: Siren,
      styles: 'bg-red-100 text-red-900 border-red-300 shadow-md ring-2 ring-red-400 ring-opacity-50',
      iconColor: 'text-red-700',
      pulse: true
    },
    'High': {
      icon: Flame,
      styles: 'bg-red-100 text-red-800 border-red-200 shadow-sm',
      iconColor: 'text-red-600',
      pulse: true
    },
    'Medium': {
      icon: Activity,
      styles: 'bg-yellow-100 text-yellow-800 border-yellow-200 shadow-sm',
      iconColor: 'text-yellow-600',
      animation: 'hover:scale-105 transition-transform duration-200'
    },
    'Low': {
      icon: LeafyGreen,
      styles: 'bg-green-100 text-green-800 border-green-200 shadow-sm',
      iconColor: 'text-green-600'
    },
    'default': {
      icon: AlertTriangle,
      styles: 'bg-gray-100 text-gray-600 border-gray-200 shadow-sm',
      iconColor: 'text-gray-500'
    }
  };

  const config = priorityConfig[normalizedPriority as Priority] || priorityConfig.default;
  const Icon = config.icon;

  return (
    <span
      className={`
      inline-flex items-center px-2 py-1 typography-caption font-weight-medium
      rounded-full border whitespace-nowrap ${config.styles} ${config.pulse ? 'animate-pulse' : ''}
      ${config.animation || ''} cursor-default
    `}
    >
      <Icon className={`w-3 h-3 mr-1.5 ${config.iconColor}`} />
      {normalizedPriority || 'Not Set'}
      {isDropdown && <ChevronDown className="w-3 h-3 ml-1" />}
    </span>
  );
};

// Backend Pagination component
const BackendPagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalItems = 0,
  perPage,
  onPageChange,
  onPageSizeChange
}) => {
  const pageSizeOptions = [10, 20, 50, 100];

  // Generate page numbers to display
  const getPageNumbers = () => {
    const delta = 1; // Number of pages to show on each side of current page
    const range = [];

    // Always include first page
    range.push(1);

    // Calculate start and end of range
    const rangeStart = Math.max(2, currentPage - delta);
    const rangeEnd = Math.min(totalPages - 1, currentPage + delta);

    // Add dots after first page if needed
    if (rangeStart > 2) {
      range.push(-1); // Use -1 to represent dots
    }

    // Add all pages in the range
    for (let i = rangeStart; i <= rangeEnd; i++) {
      range.push(i);
    }

    // Add dots before last page if needed
    if (rangeEnd < totalPages - 1) {
      range.push(-2); // Use -2 to represent dots (different key)
    }

    // Always include last page if not the same as first
    if (totalPages > 1) {
      range.push(totalPages);
    }

    return range;
  };

  return (
    <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
      <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">

        <div className="flex items-center space-x-4">
          {onPageSizeChange && (
            <div className="flex items-center space-x-2">
              <span className="typography-body-sm text-gray-700">Show</span>
              <select
                className="rounded border-gray-300 typography-body-sm focus:border-blue-500 focus:ring-blue-500"
                value={perPage}
                onChange={(e) => onPageSizeChange(Number(e.target.value))}
              >
                {pageSizeOptions.map(size => (
                  <option key={size} value={size}>
                    {size}
                  </option>
                ))}
              </select>
            </div>
          )}

          <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage <= 1}
              className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white typography-body-sm font-weight-medium ${
                currentPage <= 1
                  ? 'text-gray-300 cursor-not-allowed'
                  : 'text-gray-500 hover:bg-gray-50 cursor-pointer'
              }`}
            >
              <span className="sr-only">Previous</span>
              <ChevronUp className="h-5 w-5 rotate-270" />
            </button>

            {getPageNumbers().map((pageNum, idx) => (
              pageNum < 0 ? (
                <span key={pageNum} className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white typography-body-sm font-weight-medium text-gray-700">
                  ...
                </span>
              ) : (
                <button
                  key={pageNum}
                  onClick={() => onPageChange(pageNum)}
                  className={`relative inline-flex items-center px-4 py-2 border typography-body-sm font-weight-medium ${
                    currentPage === pageNum
                      ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  {pageNum}
                </button>
              )
            ))}

            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage >= totalPages}
              className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white typography-body-sm font-weight-medium ${
                currentPage >= totalPages
                  ? 'text-gray-300 cursor-not-allowed'
                  : 'text-gray-500 hover:bg-gray-50 cursor-pointer'
              }`}
            >
              <span className="sr-only">Next</span>
              <ChevronDown className="h-5 w-5" />
            </button>
          </nav>
        </div>
      </div>
    </div>
  );
};

const ScmTable: React.FC<ScmTableProps> = ({
  headers,
  data = [],
  onRowClick,
  sortableColumns = {},
  onActionClick,
  title,
  pagination,
  onSortChange,
  isLoading = false
}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: 'ascending' });

  const TableHeader: React.FC<{ header: HeaderProps }> = ({ header }) => (
    <th
      key={header.key}
      onClick={() => sortableColumns[header.key] && requestSort(header.key)}
      className={`px-6 py-2.5 typography-heading-6 text-[#687182] uppercase ${
        ['id', 'action', 'status', 'type'].includes(header.key) ? 'text-center' : ''
      } ${sortableColumns[header.key] ? 'cursor-pointer hover:bg-gray-50' : ''}`}
      style={{ minWidth: header.key.toLowerCase() === 'title' ? '200px' : 'auto' }}
    >
      <div className={`flex items-center space-x-1 ${
        ['id', 'action', 'status', 'type'].includes(header.key) ? 'justify-center' : ''
      }`}>
        <span>{header.label}</span>
        {sortableColumns[header.key] && (
          <div className="flex flex-col">
            <ChevronUp
              className={`h-3 w-3 ${
                sortConfig.key === header.key && sortConfig.direction === 'ascending'
                  ? 'text-blue-600'
                  : 'text-[#A1A9B8]'
              }`}
            />
            <ChevronDown
              className={`h-3 w-3 ${
                sortConfig.key === header.key && sortConfig.direction === 'descending'
                  ? 'text-blue-600'
                  : 'text-[#A1A9B8]'
              }`}
            />
          </div>
        )}
      </div>
    </th>
  );

  const requestSort = (key: string) => {
    if (!sortableColumns[key]) return;

    const newDirection =
      sortConfig.key === key && sortConfig.direction === 'ascending'
        ? 'descending'
        : 'ascending';

    setSortConfig({ key, direction: newDirection });

    // Notify parent component for backend sorting
    if (onSortChange) {
      onSortChange(key, newDirection);
    }
  };

  return (
    <div className="w-full bg-white border border-[#E9EDF5] rounded-lg shadow">
      {title && (
        <div className="bg-gray-100 px-4 py-2 font-weight-medium text-gray-700">
          {title}
        </div>
      )}

      <div className="overflow-x-auto w-full">
        <table className="w-full border-collapse divide-y divide-[#E9EDF5]">
          <thead className="bg-[#F7F9FCCC]">
            <tr>
              {headers.map((header) => (
                <TableHeader key={header.key} header={header} />
              ))}
            </tr>
          </thead>

          <tbody className="bg-white divide-y divide-[#E9EDF5]">
            {isLoading ? (
              <tr>
                <td colSpan={headers.length} className="px-6 py-4 text-center">
                  <div className="flex justify-center items-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <span className="ml-3">Loading...</span>
                  </div>
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={headers.length} className="px-6 py-4 text-center text-gray-500">
                  No data available
                </td>
              </tr>
            ) : (
              data.map((row, index) => (
                <tr
                  key={row.id || row.repositoryId || index}
                  onClick={() => onRowClick(row.id || row.repositoryId, row.type)}
                  className="hover:bg-gray-50 cursor-pointer transition-colors"
                >
                  {headers.map((header) => (
                    <td
                      key={header.key}
                      className={`px-6 py-2.5 ${
                        ['id', 'action', 'status', 'type'].includes(header.key) ? 'text-center' : ''
                      } ${header.key === 'Priority' ? 'text-center' : ''}`}
                      style={{
                        minWidth: header.key.toLowerCase() === 'title'
                          ? title === 'System Containers' ? '320px' : '230px'
                          : header.key.toLowerCase() === 'assignee'
                            ? '230px'
                            : header.key.toLowerCase() === 'description'
                              ? '150px'
                              : 'auto'
                      }}
                    >
                      {header.key === 'status' ? (
                        <StatusBadge status={row[header.key]} />
                      ) : header.key === 'action' ? (
                        <DynamicButton
                          type="submit"
                          variant="primaryOutline"
                          icon={CodeXml}
                          onClick={(e) => {
                            e.stopPropagation();
                            onActionClick?.(row.id || row.repositoryId);
                          }}
                          text={header.actionLabel || 'Action'}
                        />
                      ) : header.key === 'type' ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full typography-caption font-weight-medium bg-gray-100 text-[#2A3439]">
                          {row[header.key]}
                        </span>
                      ) : header.key === 'visibility' ? (
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full typography-caption font-weight-medium
                          ${row[header.key] === 'private' ? 'bg-red-100 text-red-800' :
                            row[header.key] === 'public' ? 'bg-green-100 text-green-800' :
                              'bg-yellow-100 text-yellow-800'}`}>
                          {row[header.key]}
                        </span>
                      ) : header.key === 'repositoryName' || header.key === 'full_name' ? (
                        <div className="flex items-center">
                          <Github className="mr-2 text-blue-500" size={16} />
                          {row[header.key]}
                        </div>
                      ) : header.key === 'updated_at' || header.key === 'created_at' || header.key === 'last_activity_at' ? (
                        <div className="typography-body-sm text-[#2A3439]">
                          {row[header.key]}
                        </div>
                      ) : header.key === 'Priority' ? (
                        <PriorityBadge priority={row[header.key]} />
                      ) : (
                        <div className={`typography-body-sm text-[#2A3439] custom-table-text-ellipsis ${
                          header.key.toLowerCase() === "title" ? "hover:text-blue-600" : ""
                        }`}
                        title={header.key.toLowerCase() === "description" ? row[header.key] : undefined}>
                          {row[header.key]}
                        </div>
                      )}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {pagination && (
        <BackendPagination
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          totalItems={pagination.totalItems}
          perPage={pagination.perPage}
          onPageChange={pagination.onPageChange}
          onPageSizeChange={pagination.onPageSizeChange}
        />
      )}
    </div>
  );
};

export default ScmTable;
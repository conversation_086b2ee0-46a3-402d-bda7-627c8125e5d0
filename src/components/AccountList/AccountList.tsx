import React from "react";
import AccountCard from "./AccountCard";

interface AccountListProps {
    title?: string;
    accountList?: any[];
    onIntegrationClick?(): void;
    onCardClick?(cardData: any): void;
}

const AccountList: React.FC<AccountListProps> = ({ title, accountList, onIntegrationClick, onCardClick }) => {
    return (
        <div className="container mx-auto p-6 relative">
            <div className="flex justify-between items-center mb-6">
                <nav className="flex text-black typography-heading-4 font-weight-bold">
                    <span
                        className="text-gray-500 hover:text-black cursor-pointer"
                        onClick={onIntegrationClick}
                    >
                        Integrations
                    </span>
                    <span className="mx-2">/</span>
                    <span className="text-black">{title}</span>
                </nav>

                <button className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition duration-200">
                    Connect
                </button>
            </div>

            <div className="space-y-4 relative">
                {accountList && accountList.map((account, index) => (
                    <AccountCard
                        key={index}
                        title={account.title}
                        isConnected={account.isConnected}
                        togglebuttonsList={account.togglebuttonsList}
                        id={account.id}
                        lastSynced={account.lastSynced}
                        onCardClick={() => onCardClick && onCardClick(account)}
                    />
                ))}
            </div>
        </div>
    );
};

export default AccountList;

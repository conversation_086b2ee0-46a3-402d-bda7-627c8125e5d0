import React, { useEffect } from 'react';
import { X, <PERSON>ader2, <PERSON>f<PERSON><PERSON><PERSON>, AlertCircle, Check } from 'lucide-react';
import { useStripeModal } from './hooks/useStripe';

interface StripeConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConnectionComplete: () => void;
}

type ConnectionStep = 'api-key' | 'verifying' | 'product-selection' | 'completing' | 'success';

const StripeConnectionModal: React.FC<StripeConnectionModalProps> = ({
  isOpen,
  onClose,
  onConnectionComplete
}) => {
  // Use the new integrated hook
  const stripeModal = useStripeModal();

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      stripeModal.resetModal();
    }
  }, [isOpen, stripeModal.resetModal]);

  // Map hook state to component state for UI compatibility
  const currentStep = stripeModal.currentStep;
  const apiKey = stripeModal.apiKey;
  const products = stripeModal.products.products;
  const loading = stripeModal.products.loading;
  const error = stripeModal.error;

  const handleApiKeySubmit = async () => {
    const success = await stripeModal.handleApiKeySubmit();
    // The hook handles all the logic and state updates
  };

  const handleProductToggle = (productId: string) => {
    stripeModal.products.toggleProductSelection(productId);
  };

  const handleUpdateProducts = async () => {
    await stripeModal.handleProductRefresh();
  };

  const handleCompleteSetup = async () => {
    const success = await stripeModal.handleCompleteSetup();
    if (success) {
      // Auto close and notify parent after showing success
      setTimeout(() => {
        onConnectionComplete();
        onClose();
      }, 1500);
    }
  };

  if (!isOpen) return null;

  const renderBreadcrumb = () => {
    const steps = currentStep === 'api-key' || currentStep === 'verifying' 
      ? ['Configure App', 'Connect Stripe']
      : ['Configure App', 'Connect Stripe', 'Select Products'];
    
    return (
      <div className="flex items-center space-x-1 text-xs">
        {steps.map((step, index) => (
          <React.Fragment key={step}>
            <span className={index === 0 ? 'text-blue-600 hover:underline cursor-pointer' : index === steps.length - 1 ? 'text-slate-700 font-medium' : 'text-slate-500'}>
              {step}
            </span>
            {index < steps.length - 1 && <span className="text-slate-500"> › </span>}
          </React.Fragment>
        ))}
      </div>
    );
  };

  const renderContent = () => {
    switch (currentStep) {
      case 'api-key':
        return (
          <>
            <div className="flex-1 flex flex-col">
              <div className="flex flex-col items-center mb-6 text-center">
                {/* Stripe Logo */}
                <div className="mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="64" height="64" fill="#6772e5"><path d="M111.328 15.602c0-4.97-2.415-8.9-7.013-8.9s-7.423 3.924-7.423 8.863c0 5.85 3.32 8.8 8.036 8.8 2.318 0 4.06-.528 5.377-1.26V19.22a10.246 10.246 0 0 1-4.764 1.075c-1.9 0-3.556-.67-3.774-2.943h9.497a39.64 39.64 0 0 0 .063-1.748zm-9.606-1.835c0-2.186 1.35-3.1 2.56-3.1s2.454.906 2.454 3.1zM89.4 6.712a5.434 5.434 0 0 0-3.801 1.509l-.254-1.208h-4.27v22.64l4.85-1.032v-5.488a5.434 5.434 0 0 0 3.444 1.265c3.472 0 6.64-2.792 6.64-8.957.003-5.66-3.206-8.73-6.614-8.73zM88.23 20.1a2.898 2.898 0 0 1-2.288-.906l-.03-7.2a2.928 2.928 0 0 1 2.315-.96c1.775 0 2.998 2 2.998 4.528.003 2.593-1.198 4.546-2.995 4.546zM79.25.57l-4.87 1.035v3.95l4.87-1.032z" fill-rule="evenodd"/><path d="M74.38 7.035h4.87V24.04h-4.87z"/><path d="M69.164 8.47l-.302-1.434h-4.196V24.04h4.848V12.5c1.147-1.5 3.082-1.208 3.698-1.017V7.038c-.646-.232-2.913-.658-4.048 1.43zm-9.73-5.646L54.698 3.83l-.02 15.562c0 2.87 2.158 4.993 5.038 4.993 1.585 0 2.756-.302 3.405-.643v-3.95c-.622.248-3.683 1.138-3.683-1.72v-6.9h3.683V7.035h-3.683zM46.3 11.97c0-.758.63-1.05 1.648-1.05a10.868 10.868 0 0 1 4.83 1.25V7.6a12.815 12.815 0 0 0-4.83-.888c-3.924 0-6.557 2.056-6.557 5.488 0 5.37 7.375 4.498 7.375 6.813 0 .906-.78 1.186-1.863 1.186-1.606 0-3.68-.664-5.307-1.55v4.63a13.461 13.461 0 0 0 5.307 1.117c4.033 0 6.813-1.992 6.813-5.485 0-5.796-7.417-4.76-7.417-6.943zM13.88 9.515c0-1.37 1.14-1.9 2.982-1.9A19.661 19.661 0 0 1 25.6 9.876v-8.27A23.184 23.184 0 0 0 16.862.001C9.762.001 5 3.72 5 9.93c0 9.716 13.342 8.138 13.342 12.326 0 1.638-1.4 2.146-3.37 2.146-2.905 0-6.657-1.202-9.6-2.802v8.378A24.353 24.353 0 0 0 14.973 32C22.27 32 27.3 28.395 27.3 22.077c0-10.486-13.42-8.613-13.42-12.56z" fill-rule="evenodd"/></svg>
                </div>
                
                <h2 className="text-xl font-semibold text-slate-800">
                  Connect to Stripe
                </h2>
              </div>
              
              <div className="w-full">
                <label className="block text-sm font-medium text-slate-600 mb-1">
                  Enter your Stripe API Key
                </label>
                <input
                  type="text"
                  value={apiKey}
                  onChange={(e) => stripeModal.setApiKey(e.target.value)}
                  placeholder="sk_test_xxxxxxxxxxxxx or pk_test_xxxxxxxxxxx"
                  className="w-full px-3 py-2.5 border border-slate-300 rounded-md text-sm placeholder:text-gray-400 focus:ring-2 focus:ring-[#635BFF] focus:border-[#635BFF] outline-none transition-colors"
                />
                <p className="mt-1 text-xs text-slate-500">
                  You can find your API keys in your Stripe Dashboard under Developers › API Keys.
                </p>
                
                {error && (
                  <div className="mt-4 flex items-center text-red-600 text-sm">
                    <AlertCircle size={16} className="mr-2" />
                    {error}
                  </div>
                )}
              </div>
            </div>
            
            <div className=" -mx-6 -mb-6 px-6 pt-6 pb-6 border-slate-200 rounded-b-xl">
                <div className="flex justify-end space-x-3">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 bg-slate-200 text-slate-700 rounded-md hover:bg-slate-300 text-sm font-medium transition-colors"
                    >
                        Back
                    </button>
                    <button
                        onClick={handleApiKeySubmit}
                        className="px-4 py-2 bg-[#635BFF] text-white rounded-md hover:bg-[#5147E5] text-sm font-medium transition-colors"
                    >
                        Connect
                    </button>
                </div>
            </div>
          </>
        );

      case 'verifying':
        return (
          <div className="flex flex-col items-center justify-center py-20">
            <Loader2 className="w-12 h-12 text-purple-600 animate-spin mb-4" />
            <p className="text-gray-600">Verifying API Key...</p>
          </div>
        );

      case 'product-selection':
        return (
          <>
            <div className="flex flex-col items-center text-center">
              <div className="mb-4">
              <svg width="32" height="29" viewBox="0 0 32 29" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M30.5 14.1875C31.375 14.5 32 15.3125 32 16.25V23.125C32 24 31.5 24.8125 30.75 25.1875L24.5 28.3125C23.875 28.625 23.125 28.625 22.4375 28.3125L16 25.0625L9.5 28.3125C8.875 28.625 8.125 28.625 7.4375 28.3125L1.1875 25.1875C0.4375 24.8125 0 24 0 23.125V16.25C0 15.3125 0.5625 14.5 1.4375 14.1875L7.4375 11.875V5.125C7.4375 4.1875 8.0625 3.375 8.9375 3L15.1875 0.6875C15.6875 0.5 16.25 0.5 16.75 0.6875L23 3C23.875 3.375 24.5 4.1875 24.5 5.125V11.875L30.5 14.1875ZM22.375 11.9375V7.375L17.0625 9.6875V13.9375L22.375 11.9375ZM9.625 5.0625L16 7.6875L22.375 5.0625L16 2.625L9.625 5.0625ZM14.875 23.25V18.5L9.5625 20.9375V25.875L14.875 23.25ZM14.875 16.25V16.1875L8.5 13.8125L2.125 16.1875V16.25L8.5 18.8125L14.875 16.25ZM29.875 23.25V18.5L24.5625 20.9375V25.875L29.875 23.25ZM29.875 16.25V16.1875L23.5 13.8125L17.125 16.1875V16.25L23.5 18.8125L29.875 16.25Z" fill="#635BFF"/>
</svg>

              </div>
              <h2 className="text-xl font-semibold text-slate-800 mb-2">
                Select Your Stripe Products
              </h2>
              <p className="text-sm text-slate-600 mb-6 max-w-md mx-auto">
                Choose the Stripe products you want to integrate with your application.
              </p>
            </div>
            
            <div className="w-full max-w-lg mx-auto">
                {products.length > 0 && (
                  <div className="flex justify-end mb-2">
                      <button
                          onClick={handleUpdateProducts}
                          disabled={loading}
                          className="flex items-center text-sm font-medium text-[#635BFF] hover:text-[#5147E5]"
                      >
                          {loading ? (
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          ) : (
                              <RefreshCw className="w-4 h-4 mr-2" />
                          )}
                          Update my products
                      </button>
                  </div>
                )}

                <div className="border border-slate-200 rounded-lg bg-white">
                    {products.length > 0 ? (
                        <div className="divide-y divide-slate-200">
                            {products.map((product) => (
                                <label
                                    key={product.id}
                                    className="flex items-center w-full p-4 cursor-pointer hover:bg-slate-50"
                                >
                                    <input
                                        type="checkbox"
                                        checked={product.selected}
                                        onChange={() => handleProductToggle(product.id)}
                                        className="h-4 w-4 rounded border-slate-300 text-[#635BFF] focus:ring-[#635BFF] focus:ring-offset-0"
                                    />
                                    <span className="ml-3 text-sm font-medium text-slate-800">{product.name}</span>
                                </label>
                            ))}
                        </div>
                    ) : (
                        <div className="py-12 px-6 text-center">
                            <AlertCircle className="w-10 h-10 text-slate-400 mx-auto mb-4" />
                            <p className="text-slate-700 font-medium mb-2">No products found</p>
                            <p className="text-sm text-slate-500 mb-6">
                                Create products in your Stripe Dashboard first.
                            </p>
                            <button
                                onClick={handleUpdateProducts}
                                disabled={loading}
                                className="inline-flex items-center text-sm font-medium text-[#635BFF] hover:text-[#5147E5]"
                            >
                                {loading ? (
                                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                ) : (
                                    <RefreshCw className="w-4 h-4 mr-2" />
                                )}
                                Sync products
                            </button>
                        </div>
                    )}
                </div>
                
                {error && (
                    <div className="mt-4 flex items-center text-red-600 text-sm">
                      <AlertCircle size={16} className="mr-2" />
                      {error}
                    </div>
                  )}
            </div>

            <div className="flex justify-end space-x-3 mt-8">
                <button
                    onClick={onClose}
                    className="px-4 py-2 bg-slate-100 text-slate-700 rounded-md hover:bg-slate-200 text-sm font-medium"
                >
                    Back
                </button>
                <button
                    onClick={handleCompleteSetup}
                    disabled={products.length === 0}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                        products.length === 0
                            ? 'bg-slate-200 text-slate-500 cursor-not-allowed'
                            : 'bg-[#635BFF] text-white hover:bg-[#5147E5]'
                    }`}
                >
                    Complete Setup
                </button>
            </div>
          </>
        );

      case 'completing':
        return (
          <div className="flex flex-col items-center justify-center py-20">
            <Loader2 className="w-12 h-12 text-purple-600 animate-spin mb-4" />
            <p className="text-gray-600">Completing Stripe Setup</p>
          </div>
        );

      case 'success':
        return (
          <div className="flex flex-col items-center justify-center py-20">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mb-4">
              <Check className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Successfully Connected!
            </h3>
            <p className="text-gray-600">
              Your Stripe integration is now active
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl w-full max-w-[708px] max-h-[810px] shadow-[0px_8px_10px_-6px_rgba(0,0,0,0.10)] shadow-xl overflow-hidden flex flex-col">
        {/* Header */}
        <div className="px-6 pt-6 pb-[25px] border-b border-slate-200 flex justify-between items-center">
          <div className="flex flex-col gap-[3.5px]">
            <h1 className="text-xl font-semibold text-slate-800">
              {currentStep === 'product-selection' ? 'Select Your Stripe Products' : 'Connect to Stripe'}
            </h1>
            {renderBreadcrumb()}
          </div>
          <button
            onClick={onClose}
            className="text-slate-500 hover:text-slate-700 transition-colors p-[11.5px]"
          >
            <X size={20} strokeWidth={3} />
          </button>
        </div>
        
        {/* Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default StripeConnectionModal;
'use client';

import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { Download, FileText, FileSpreadsheet, FileCog } from 'lucide-react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';

export function DocumentGrid({ documents, onDownload }) {
  const searchParams = useSearchParams()
  const router = useRouter()

  const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    switch (extension) {
      case 'pdf':
        return FileCog;
      case 'doc':
      case 'docx':
        return FileSpreadsheet;
      default:
        return FileText;
    }
  };

  const projectId = useParams().projectId
  const currentType = searchParams.get('type', 'PRD')
  const handleDocumentClick = (document) => {
    router.push(`documents/view?projectId=${projectId}&docType=${currentType}&version=${document.version}&fileName=${encodeURIComponent(document.file_name)}`);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 gap-4">
      {documents.map((document, index) => {
        const FileIcon = getFileIcon(document.file_name);
        return (
          <div
            key={`${document.file_name}-${index}`}
            className="w-full bg-white rounded-lg border border-gray-200 hover:border-blue-500 transition-colors cursor-pointer"
            onClick={() => handleDocumentClick(document)}
          >
            <div className="relative p-4 bg-gray-50 flex justify-center items-center">
              <FileIcon 
                className="w-12 h-12 text-gray-400"
              />
              <button 
                className="absolute top-2 right-2 p-1.5 hover:bg-gray-100 rounded-full text-blue-600"
                onClick={(e) => {
                  e.stopPropagation();
                  onDownload(document);
                }}
              >
                <Download width="20" height="20" />
              </button>
            </div>

            <div className="p-4">
              <h3 className="typography-body-sm font-weight-medium text-gray-900 truncate">
                {document.file_name}
              </h3>
              <div className="mt-1 flex items-center typography-caption text-gray-500">
                <span className="truncate">
                  {formatDistanceToNow(new Date(document.last_modified), { addSuffix: true })}
                </span>
                <span className="mx-1">•</span>
                <span>{formatFileSize(document.size)}</span>
              </div>
              <div className="mt-1 typography-caption text-gray-500">
                Version {document.version}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
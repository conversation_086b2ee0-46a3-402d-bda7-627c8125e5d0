import React, { useEffect, useState } from "react";
import {
  X,
  AlertCircle,
  CheckCircle,
  Loader,
  ExternalLink,
  GitBranch,
  Server,
  Rocket,
} from "lucide-react";
import { getHeaders } from "@/utils/api";
import Confetti from "react-confetti";

interface DeploymentStep {
  status: "in_progress" | "completed" | "error";
  step: string;
  message: string;
  data?: any;
}

interface DeploymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string;
  containerId: string;
  branch: string;
}

export const DeploymentModal = ({
  isOpen,
  onClose,
  projectId,
  containerId,
  branch,
}: DeploymentModalProps) => {
  const [deploymentPhase, setDeploymentPhase] = useState<
    "infrastructure" | "code_push" | "checking_deployment"
  >("infrastructure");
  const [steps, setSteps] = useState<{ [key: string]: DeploymentStep }>({});
  const [codePushStreamComplete, setCodePushStreamComplete] = useState(false);
  const [currentStep, setCurrentStep] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [infraCompleted, setInfraCompleted] = useState(false);
  const [codePushCompleted, setCodePushCompleted] = useState(false);
  const [deploymentDomain, setDeploymentDomain] = useState<string | null>(null);
  const [showConfetti, setShowConfetti] = useState(false);
  const [repoUrl, setRepoUrl] = useState<string | null>(null);


  useEffect(() => {
    const fetchRepoUrl = async () => {
      try {
        const headers = await getHeaders();
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/repository/get_repository/${projectId}/?container_id=${containerId}`,
          {
            headers,
          }
        );
        
        if (!response.ok) {
          throw new Error('Failed to fetch repository URL');
        }
        
        const data = await response.json();
        if (data.repository?.cloneUrlHttp) {
          setRepoUrl(data.repository.cloneUrlHttp);
        }
      } catch (error) {
        
        setError('Failed to fetch repository URL');
      }
    };
  
    if (isOpen && !repoUrl) {
      fetchRepoUrl();
    }
  }, [isOpen, projectId, containerId]);


  useEffect(() => {
    if (!isOpen) return;
    startInfraDeployment();
  }, [isOpen]);

  useEffect(() => {
    const deploymentStep = steps["deployment"];
    if (deploymentStep && deploymentStep.status === "completed") {
      setInfraCompleted(true);
    }
  }, [steps]);

  const startInfraDeployment = async () => {
    setIsLoading(true);
    setError(null);
    setSteps({});
    setDeploymentPhase("infrastructure");
    setInfraCompleted(false);
    setCodePushCompleted(false);
    setDeploymentDomain(null);
    setShowConfetti(false);

    try {
      const headers = await getHeaders();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/auth/deployment/${projectId}/${containerId}/${branch}/deploy_infrastructure`,
        {
          method: "POST",
          headers: {
            ...headers,
            Accept: "text/event-stream",
          },
        }
      );

      if (!response.ok)
        throw new Error(`Deployment failed: ${response.statusText}`);

      const reader = response.body?.getReader();
      if (!reader) throw new Error("Failed to initialize stream reader");

      const decoder = new TextDecoder();

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split("\n");

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.step) {
                setSteps((prev) => ({
                  ...prev,
                  [data.step]: data,
                }));
                setCurrentStep(data.step);

                if (data.status === "error") {
                  setError(data.message);
                }
              }
            } catch (e) {
              
            }
          }
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An unknown error occurred";
      
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const startCodePush = async () => {
    if (!repoUrl) {
      setError("Repository URL not found");
      return;
    }

    setIsLoading(true);
    setError(null);
    setSteps({});
    setDeploymentPhase("code_push");
    // Reset both states at the start
    setCodePushStreamComplete(false);
    setCodePushCompleted(false);

    try {
      const headers = await getHeaders();
      const encodedRepoUrl = encodeURIComponent(repoUrl);

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/auth/deployment/${projectId}/${containerId}/${branch}/push_to_new_repo?repo_url=${encodedRepoUrl}`,
        {
          method: "POST",
          headers: {
            ...headers,
            Accept: "text/event-stream",
          },
        }
      );

      if (!response.ok)
        throw new Error(`Code push failed: ${response.statusText}`);

      const reader = response.body?.getReader();
      if (!reader) throw new Error("Failed to initialize stream reader");

      const decoder = new TextDecoder();

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split("\n");

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.step) {
                setSteps((prev) => ({
                  ...prev,
                  [data.step]: data,
                }));
                setCurrentStep(data.step);

                if (data.status === "completed" && data.step === "completion") {
                  setCodePushCompleted(true);
                  setCodePushStreamComplete(true);
                }

                if (data.status === "error") {
                  setError(data.message);
                  setCodePushCompleted(false);
                  setCodePushStreamComplete(false);
                }
              }
            } catch (e) {
              
            }
          }
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An unknown error occurred";
      
      setError(errorMessage);
      // Reset states on error
      setCodePushCompleted(false);
      setCodePushStreamComplete(false);
    } finally {
      setIsLoading(false);
    }
  };
  const checkDeploymentStatus = async () => {
    setIsLoading(true);
    setError(null);
    setDeploymentPhase("checking_deployment");

    try {
      const headers = await getHeaders();
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/auth/deployment/d2akdn0zju9cbo/conflict-resolution-8d71f9cd/deployment-status`,
        {
          headers: {
            ...headers,
            Accept: "text/event-stream",
          },
        }
      );

      if (!response.ok)
        throw new Error(`Deployment check failed: ${response.statusText}`);

      const reader = response.body?.getReader();
      if (!reader) throw new Error("Failed to initialize stream reader");

      const decoder = new TextDecoder();

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split("\n");

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.step) {
                setSteps((prev) => ({
                  ...prev,
                  [data.step]: data,
                }));
                setCurrentStep(data.step);

                if (data.status === "completed" && data.data?.domain) {
                  setDeploymentDomain(data.data.domain);
                  setShowConfetti(true);
                  setTimeout(() => setShowConfetti(false), 10000);
                }

                if (data.status === "error") {
                  setError(data.message);
                }
              }
            } catch (e) {
              
            }
          }
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An unknown error occurred";
      
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const getStepIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case "error":
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Loader className="w-5 h-5 text-blue-500 animate-spin" />;
    }
  };

  const getStepClass = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-50 border-green-100";
      case "error":
        return "bg-red-50 border-red-100";
      default:
        return "bg-blue-50 border-blue-100";
    }
  };

  const openDeployedApp = () => {
    if (deploymentDomain) {
      window.open(deploymentDomain, "_blank");
    }
  };

  const renderPhaseContent = () => {
    switch (deploymentPhase) {
      case "infrastructure":
        return (
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="flex items-center text-blue-800 font-weight-medium mb-2">
                <Server className="w-5 h-5 mr-2" />
                Infrastructure Deployment
              </h3>
              <p className="text-blue-600 typography-body-sm mb-4">
                Setting up AWS Amplify infrastructure for your application.
              </p>
              {renderSteps(steps, "infrastructure")}
            </div>
          </div>
        );

      case "code_push":
        return (
          <div className="space-y-4">
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="flex items-center text-purple-800 font-weight-medium mb-2">
                <GitBranch className="w-5 h-5 mr-2" />
                Code Push
              </h3>
              <p className="text-purple-600 typography-body-sm mb-4">
                Pushing your code to the deployment repository.
              </p>
              {renderSteps(steps, "code_push")}
            </div>
          </div>
        );

      case "checking_deployment":
        return (
          <div className="space-y-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="flex items-center text-green-800 font-weight-medium mb-2">
                <Rocket className="w-5 h-5 mr-2" />
                Deployment Status
              </h3>
              <p className="text-green-600 typography-body-sm mb-4">
                Checking the status of your application deployment.
              </p>
              {renderSteps(steps, "checking_deployment")}
            </div>
          </div>
        );
    }
  };

  const renderSteps = (
    steps: { [key: string]: DeploymentStep },
    phase: string
  ) => {
    const phaseSteps = Object.entries(steps).filter(([key, step]) => {
      switch (phase) {
        case "infrastructure":
          return !["clone", "git_operations", "deployment_check"].includes(key);
        case "code_push":
          return ["clone", "git_operations"].includes(key);
        case "checking_deployment":
          return ["deployment_check", "deployment"].includes(key);
        default:
          return false;
      }
    });

    return (
      <div className="space-y-3">
        {phaseSteps.map(([key, step]) => (
          <div
            key={key}
            className={`p-4 rounded-lg border ${getStepClass(step.status)}`}
          >
            <div className="flex items-start gap-3">
              {getStepIcon(step.status)}
              <div className="flex-1">
                <h4 className="font-weight-medium text-gray-900 capitalize">
                  {step.step.replace(/_/g, " ")}
                </h4>
                <p className="typography-body-sm mt-1 text-gray-600">{step.message}</p>
                {step.data && step.data.next_steps && (
                  <div className="mt-3">
                    <h5 className="typography-body-sm font-weight-medium text-gray-900">
                      Next Steps:
                    </h5>
                    <ul className="mt-2 space-y-1">
                      {step.data.next_steps.map(
                        (nextStep: string, index: number) => (
                          <li
                            key={index}
                            className="typography-body-sm text-gray-600 flex items-start gap-2"
                          >
                            <div className="rounded-full bg-gray-200 w-4 h-4 flex items-center justify-center typography-caption mt-0.5">
                              {index + 1}
                            </div>
                            {nextStep}
                          </li>
                        )
                      )}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-semantic-gray-900/5 flex items-center justify-center z-50 p-4">
      {showConfetti && <Confetti />}
      <div className="bg-custom-bg-primary rounded-lg w-full max-w-3xl">
        <div className="border-b border-custom-border p-4 flex justify-between items-center">
          <div className="flex-1">
            <h2 className="typography-heading-4 font-weight-semibold text-custom-text-primary">
              Deployment Progress
            </h2>
            <div className="mt-2 flex items-center space-x-4">
              <div className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    deploymentPhase === "infrastructure"
                      ? "bg-semantic-blue-100 text-semantic-blue-600"
                      : infraCompleted
                      ? "bg-semantic-green-100 text-semantic-green-600"
                      : "bg-semantic-gray-100 text-semantic-gray-400"
                  }`}
                >
                  <Server className="w-4 h-4" />
                </div>
                <div className="ml-2">
                  <p
                    className={`typography-body-sm font-weight-medium ${
                      deploymentPhase === "infrastructure"
                        ? "text-blue-600"
                        : infraCompleted
                        ? "text-green-600"
                        : "text-gray-400"
                    }`}
                  >
                    Infrastructure
                  </p>
                </div>
              </div>
              <div className="h-0.5 w-8 bg-gray-200" />
              <div className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    deploymentPhase === "code_push"
                      ? "bg-purple-100 text-purple-600"
                      : codePushCompleted
                      ? "bg-green-100 text-green-600"
                      : "bg-gray-100 text-gray-400"
                  }`}
                >
                  <GitBranch className="w-4 h-4" />
                </div>
                <div className="ml-2">
                  <p
                    className={`typography-body-sm font-weight-medium ${
                      deploymentPhase === "code_push"
                        ? "text-purple-600"
                        : codePushCompleted
                        ? "text-green-600"
                        : "text-gray-400"
                    }`}
                  >
                    Code Push
                  </p>
                </div>
              </div>
              <div className="h-0.5 w-8 bg-gray-200" />
              <div className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    deploymentPhase === "checking_deployment"
                      ? "bg-green-100 text-green-600"
                      : deploymentDomain
                      ? "bg-green-100 text-green-600"
                      : "bg-gray-100 text-gray-400"
                  }`}
                >
                  <Rocket className="w-4 h-4" />
                </div>
                <div className="ml-2">
                  <p
                    className={`typography-body-sm font-weight-medium ${
                      deploymentPhase === "checking_deployment"
                        ? "text-green-600"
                        : deploymentDomain
                        ? "text-green-600"
                        : "text-gray-400"
                    }`}
                  >
                    Deployment
                  </p>
                </div>
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {isLoading && Object.keys(steps).length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <Loader className="w-8 h-8 text-blue-500 animate-spin" />
              <span className="ml-3 text-gray-600">
                {deploymentPhase === "infrastructure" &&
                  "Initializing deployment..."}
                {deploymentPhase === "code_push" && "Preparing code push..."}
                {deploymentPhase === "checking_deployment" &&
                  "Checking deployment status..."}
              </span>
            </div>
          ) : (
            renderPhaseContent()
          )}

          {error && (
            <div className="mt-4 p-4 bg-red-50 rounded-lg border border-red-100">
              <div className="flex items-start">
                <AlertCircle className="w-5 h-5 text-red-500 mt-0.5" />
                <div className="ml-3">
                  <h3 className="typography-body-sm font-weight-medium text-red-800">
                    Deployment Error
                  </h3>
                  <div className="mt-1 typography-body-sm text-red-700 whitespace-pre-wrap">
                    {error}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {deploymentDomain && (
          <div className="p-6 bg-green-50 border-t border-green-100">
            <div className="text-center">
              <h3 className="typography-body-lg font-weight-medium text-green-800">
                🎉 Your application has been deployed successfully!
              </h3>
              <p className="mt-2 typography-body-sm text-green-600">
                Your application is now live and ready to use.
              </p>
              <button
                onClick={openDeployedApp}
                className="mt-4 inline-flex items-center px-6 py-2 typography-body-sm font-weight-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <span>Open Deployed App</span>
                <ExternalLink className="ml-2 w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        <div className="border-t p-4 flex justify-end bg-gray-50">
          {infraCompleted && deploymentPhase === "infrastructure" && (
            <button
              onClick={startCodePush}
              className="mr-3 px-4 py-2 typography-body-sm font-weight-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
            >
              <span className="flex items-center">
                <GitBranch className="w-4 h-4 mr-2" />
                Commit the Code
              </span>
            </button>
          )}
          {codePushCompleted && !deploymentDomain && (
            <button
              onClick={checkDeploymentStatus}
              disabled={!codePushStreamComplete || isLoading}
              className={`mr-3 px-4 py-2 typography-body-sm font-weight-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 ${
                codePushStreamComplete && !isLoading
                  ? "bg-green-600 hover:bg-green-700"
                  : "bg-green-400 cursor-not-allowed"
              }`}
            >
              <span className="flex items-center">
                <Rocket className="w-4 h-4 mr-2" />
                {isLoading
                  ? "Processing..."
                  : codePushStreamComplete
                  ? "Check Deployment Status"
                  : "Waiting for code push to complete..."}
              </span>
            </button>
          )}
          <button
            onClick={onClose}
            className="px-4 py-2 typography-body-sm font-weight-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

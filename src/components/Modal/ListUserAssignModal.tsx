import React, { useEffect, useState, useRef, useContext } from 'react';
import { assignTask } from "@/utils/api";
import { AlertContext } from "../NotificationAlertService/AlertList";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { SearchInput } from '../UIComponents/Inputs/SearchInput';
import { DynamicButton } from '../UIComponents/Buttons/DynamicButton';
import { UserPlus, Users, X } from 'lucide-react';

interface User {
    Username: string;
    Name: string;
    Email: string;
    Id: string;
}

interface UserInDiscussion {
    user_id: string;
}

interface UserSelection {
    user_id: string;
    role: string;
    responsibilities: string[];
}

interface AddUserProjectProps {
    users: User[];
    show: boolean;
    setShow: (show: boolean) => void;
    nodeid?: string;
    refresh?: boolean;
    setRefresh?: (refresh: boolean) => void;
    updateNodeDetails?: (details: any) => void;
    usersInDiscussion?: UserInDiscussion[];
}

const UserAddView: React.FC<{
    user: User;
    onClick: () => void;
    isAssignable: boolean;
    isSelected: boolean;
}> = ({ user, onClick, isAssignable, isSelected }) => {
    const getInitials = (name: string) => {
        if (!name) return '';
        const nameParts = name.split(' ');
        return nameParts.length > 0
            ? nameParts.map((part) => part.charAt(0).toUpperCase()).join("")
            : user.Username.slice(0, 2).toUpperCase();
    };

    const getInitialsColor = () => {
        const hash = user.Name.split('').reduce((acc, char) => ((acc << 5) - acc) + char.charCodeAt(0), 0);
        const hue = hash % 360;
        return `hsl(${hue}, 80%, 60%)`;
    };

    const initials = getInitials(user.Name);

    return (
        <div
            onClick={onClick}
            className={`
                flex items-center p-4 rounded-lg border mb-2 transition-all cursor-pointer 
                ${!isAssignable ? 'opacity-50 cursor-not-allowed' : ''}
                ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-100 hover:bg-gray-50'}
            `}
        >
            <div
                className="relative inline-flex items-center justify-center w-10 h-10 overflow-hidden rounded-full mr-4"
                style={{ backgroundColor: getInitialsColor() }}
            >
                <span className="font-weight-medium text-white">{initials}</span>
            </div>
            <div className="flex-1">
                <h3 className="font-weight-medium text-gray-900">
                    {user.Name || "Unknown User"}
                </h3>
                <p className="typography-body-sm text-gray-500">
                    {user.Email || "No email provided"}
                </p>
            </div>
            {isAssignable && (
                <span className={`font-weight-medium ${isSelected ? 'text-blue-600' : 'text-gray-500'}`}>
                    {isSelected ? 'Selected' : 'Select'}
                </span>
            )}
        </div>
    );
};

const ListUserAssignModal: React.FC<AddUserProjectProps> = ({
    users,
    show,
    setShow,
    nodeid,
    refresh,
    setRefresh,
    updateNodeDetails,
    usersInDiscussion = [],
}) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [allUsers, setAllUsers] = useState<User[]>([]);
    const [showWarning, setShowWarning] = useState(false);
    const [selectedUser, setSelectedUser] = useState<User | null>(null);
    const { showAlert } = useContext(AlertContext);
    const modalRef = useRef<HTMLDivElement>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    useEffect(() => {
        setAllUsers(users);
    }, [users]);

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const searchTerm = e.target.value;
        setSearchTerm(searchTerm);
        const filtered = users.filter((user) =>
            user.Email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            user.Name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setAllUsers(filtered);
    };

    const handleClickOutside = (event: MouseEvent) => {
        if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
            setShow(false);
        }
    };

    useEffect(() => {
        if (show) {
            document.addEventListener("mousedown", handleClickOutside);
        } else {
            document.removeEventListener("mousedown", handleClickOutside);
        }

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [show]);

    const selectUser = (user: User) => {
        if (isUserAssignable(user)) {
            setSelectedUser(prevSelected =>
                prevSelected?.Username === user.Username ? null : user
            );
        }
    };

    const isUserAssignable = (user: User) => {
        return !usersInDiscussion.some(
            (discussionUser) => discussionUser.user_id === user.Username
        );
    };

    const clearSearch = () => {
        setSearchTerm('');
        setAllUsers(users);
    };


    const onSubmit = async () => {
        if (!selectedUser) {
            showAlert("Please select a user to assign", "warning");
            return;
        }

        setIsLoading(true);

        try {
            await assignTask(nodeid || '', selectedUser.Username);

            if (updateNodeDetails) {
                const newDetails = {
                    assignee_name: selectedUser.Name,
                    assignee_email: selectedUser.Email,
                    assignee_id: selectedUser.Id,
                    assigned_at: new Date().toISOString(),
                };

                updateNodeDetails(newDetails);
            }

            setShow(false);
            if (setRefresh && refresh !== undefined) {
                setRefresh(!refresh);
            }
            showAlert(`User ${selectedUser.Name} assigned successfully!`, "success");

            setSelectedUser(null);
        } catch (error) {
            showAlert("Failed to assign user", "error");
        } finally {
            setIsLoading(false);
        }
    };

    if (!show) return null;

    return (
        <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setShow(false)} />
            <div
                ref={modalRef}
                className="relative bg-white rounded-lg p-6 max-w-[600px] w-full max-h-[90vh] overflow-y-auto custom-scrollbar"
            >
                <div className='flex justify-between items-center mb-4'>
                    <div className="flex justify-between items-center gap-2">
                        <Users className="h-5 w-5 text-gray-500" />
                        <h2 className="typography-heading-4 font-weight-semibold">Assign User</h2>
                    </div>
                    <DynamicButton
                        variant="ghost"
                        size="sqSmall"
                        icon={X}
                        onClick={() => setShow(false)}
                        tooltip="Close"
                    />
                </div>
                <div className="relative mb-4">
                    <SearchInput
                        value={searchTerm}
                        onChange={handleSearchChange}
                        placeholder="Search users by email..."
                    />
                </div>
                <div className="max-h-[400px] overflow-y-auto pr-2 custom-scrollbar">
                    {users.length === 0 ? (
                        <EmptyStateView type="noUsers" onClick={() => { }} />
                    ) : allUsers.length > 0 ? (
                        allUsers.map((user) => (
                            <UserAddView
                                key={user.Id}
                                user={user}
                                onClick={() => selectUser(user)}
                                isAssignable={isUserAssignable(user)}
                                isSelected={selectedUser?.Username === user.Username}
                            />
                        ))
                    ) : (
                        <EmptyStateView type="noSearchResults" onClick={clearSearch} />
                    )}
                </div>
                <div className="flex justify-between items-center mt-6">
                    <span className="typography-body-sm text-gray-500">
                        {selectedUser
                            ? `${selectedUser.Name} selected`
                            : 'No user selected'}
                    </span>
                    <DynamicButton
                        variant="primary"
                        icon={UserPlus}
                        text={isLoading ? "Adding User..." : "Add Selected User"}
                        disabled={isLoading || !selectedUser}
                        isLoading={isLoading}
                        onClick={onSubmit}
                        tooltip={`Add user to project`}
                    />
                </div>
            </div>
        </div>
    );
};

export default ListUserAssignModal;
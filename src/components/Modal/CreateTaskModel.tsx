import React, { useContext, useState, useEffect, useRef } from "react";
import { createTask, getTopLevelRequirements, fetchChildRequirements } from "@/utils/api";
import { AlertContext } from "../NotificationAlertService/AlertList";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { Layers, Plus, X } from "lucide-react";

type TaskType = "Epic" | "UserStory" | "Task";

interface CreateTaskModalProps {
    isOpen: boolean;
    onClose: () => void;
    id: string;
    projectId: string;
    updateRequirements?: (requirements: any) => void;
    updateChildRequirements?: (response: any) => void;
    type: TaskType;
}

interface TaskPayload {
    Title: string;
    Description: string;
    Type: string;
    node_type: string;
    parent_id: string;
}

const CreateTaskModal: React.FC<CreateTaskModalProps> = ({
    isOpen,
    onClose,
    id,
    projectId,
    updateRequirements,
    updateChildRequirements,
    type,
}) => {
    const [title, setTitle] = useState("");
    const [description, setDescription] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const { showAlert } = useContext(AlertContext);
    const modalRef = useRef<HTMLDivElement>(null);

    const taskType = type === "Epic" ? "UserStory" : type === "UserStory" ? "Task" : "Epic";
    const nodeType = taskType; // Since they're always the same in the original code

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
                onClose();
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [onClose]);

    if (!isOpen) return null;

    const handleSubmit = async () => {
        if (!title || !description) {
            showAlert("Please fill in all fields", "danger");
            return;
        }

        setIsLoading(true);
        try {
            const newTask: TaskPayload = {
                Title: title,
                Description: description,
                Type: taskType,
                node_type: nodeType || 'Epic',
                parent_id: id
            };

            const response = await createTask(newTask);
            const requirements = await getTopLevelRequirements(projectId);
            const childRequirements = await fetchChildRequirements(id, "Requirement");

            updateRequirements?.(requirements);
            updateChildRequirements?.(response);

            showAlert(`${taskType} Created Successfully`, "success");
            setTitle("");
            setDescription("");
            onClose();
        } catch (error) {
            
            showAlert(`Creating ${taskType} Failed`, "danger");
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="fixed inset-0 bg-black/5 backdrop-blur-sm" onClick={onClose} />

            <div
                ref={modalRef}
                className="relative bg-white rounded-xl shadow-2xl w-full max-w-lg space-y-4 mx-4 my-8 max-h-[90vh] flex flex-col"
            >
                {/* Header */}
                <div className="flex items-center justify-between py-4 px-6  border-b">
                    <div className="flex justify-between items-center gap-2">
                        <Layers className="w-5 h-5 text-gray-500" />
                        <h2 className="typography-heading-4 font-weight-bold text-gray-900">Create {taskType}</h2>
                    </div>
                    <DynamicButton
                        variant="ghost"
                        size="sqSmall"
                        icon={X}
                        onClick={onClose}
                        tooltip="Close"
                    />

                </div>

                {/* Form */}
                <div className="overflow-y-auto flex-grow py-4 px-6 space-y-4 mt-8">
                    <div className="space-y-2">
                        <label className="block typography-body-sm font-weight-medium text-gray-700">
                            {taskType} Name
                        </label>
                        <input
                            type="text"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                            className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                            placeholder={`Enter ${taskType} name`}
                        />
                    </div>

                    <div className="space-y-2">
                        <label className="block typography-body-sm font-weight-medium text-gray-700">
                            {taskType} Description
                        </label>
                        <textarea
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            rows={4}
                            className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                            placeholder={`Enter ${taskType} description`}
                        />
                    </div>

                    <div className="space-y-2">
                        <label className="block typography-body-sm font-weight-medium text-gray-700">
                            Type
                        </label>
                        <select
                            defaultValue={taskType}
                            className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-white"
                        >
                            <option value={taskType}>{taskType}</option>
                        </select>
                    </div>
                </div>

                {/* Footer */}
                <div className="py-4 px-6 border-t flex justify-end gap-3">
                    <DynamicButton
                        variant="secondary"
                        onClick={onClose}
                        text="Cancel"
                    />
                    <DynamicButton
                        type="submit"
                        variant="primary"
                        disabled={isLoading}
                        isLoading={isLoading}
                        className="w-3/12"
                        text="Create"
                        icon={Plus}
                        onClick={handleSubmit}
                    />
                </div>
            </div>
        </div>
    );
};

export default CreateTaskModal;
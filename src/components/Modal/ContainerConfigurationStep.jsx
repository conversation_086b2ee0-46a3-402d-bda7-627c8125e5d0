"use client"

import { useState, useContext, useEffect } from 'react';
import { ArrowLeft, ArrowRight, Info, Upload } from 'lucide-react';
import Logo from "../../../public/logo/kavia_logo.svg";
import Image from "next/image";
import ConfigureModal from "../Modal/ConfigureModel";
import { StateContext } from '../Context/StateContext';
import PropertiesRenderer from '../UiMetadata/PropertiesRenderer';
import {
  fetchContainerWithComponent,
  fetchSystemContextWithContainers,
  getContainerFunctionalRequirements,
  createProjectGuidanceFlow,
  updateNodeByPriority
} from '@/utils/api';
import { ProjectSetupContext } from '../Context/ProjectSetupContext';
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { AlertContext } from '../NotificationAlertService/AlertList';
import EmptyStateView from './EmptyStateModal';
import PlaceholderMessage from './PlaceHolderMessage';
import TableComponent from '../SimpleTable/ArchitectureTable';
import en from "@/en.json";
import { ExecutionContext } from '../Context/ExecutionContext';
import StatusPanel from "@/components/StatusPanel/StatusPanel";

export default function ContainerConfigurationStep({ handleBackToContainers }) {
  const [containers, setContainers] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const { projectId,
    setComponentData,
    functionalRequirements,
    setFunctionalRequirements,
    ContainerData,
    setContainerData,
    containerId,
    setContainerId,
    showConfigModel,
    setShowConfigModel
  } = useContext(ProjectSetupContext);
  const [configMethod, setConfigMethod] = useState('discussion');
  const [configureModel, setConfigureModel] = useState(false);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const [skippedContainers, setSkippedContainers] = useState([]);
  const [showSkipNotification, setShowSkipNotification] = useState(false);
  const [showAllSkippedNotification, setShowAllSkippedNotification] = useState(false);
  const [containerCount, setContainerCount] = useState(0);

  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const { showAlert } = useContext(AlertContext);
  const { setIsVertCollapse } = useContext(StateContext);
  const [isLoading, setIsLoading] = useState(true)
  const selectedContainerId = searchParams.get("selectedContainerId");
  const { configStatus, currentTaskId, setAutoNavigateEnabled } = useContext(ExecutionContext)
  const [taskStatus, setTaskStatus] = useState("Idle");

  // Track URL parameters
  useEffect(() => {
    const hasDiscussionParam = searchParams.has("discussion");
    const isCreatingContainer = searchParams.has("is_creating_Container");
    const nodeId = searchParams.get("node_id");
    const nodeType = searchParams.get("node_type");

    if (nodeId && nodeType === "Container") {
      // If the discussion parameter was previously present but now removed
      if (!hasDiscussionParam && !isCreatingContainer && nodeId) {
        // Fetch updated container to check configuration status
        fetchContainerWithComponent(projectId, nodeId)
          .then(data => {
            if (data &&
              data.data &&
              data.data.container &&
              data.data.container.properties &&
              data.data.container.properties.configuration_state === "configured") {
              // Store flag to indicate container content has been configured
              sessionStorage.setItem(`openContainerContent-${projectId}-${nodeId}`, "true");

              // Log the configuration to MongoDB - updated for new backend structure
              createProjectGuidanceFlow(parseInt(projectId), {
                project_id: parseInt(projectId),
                step_name: "container_configuration",
                status: "completed",
                data: {
                  container_id: parseInt(nodeId),
                  type: "Container",
                  status: "configured"
                }
              })
                .then(result => {

                  // Refresh container data
                  fetchData(nodeId);
                  // Refresh the containers list
                  fetchContainers();
                })
                .catch(error => {

                });
            }
          })
          .catch(error => {

          });
      }
    }
  }, [searchParams, projectId]);

  const fetchData = async (containerId) => {
    if (!containerId) return; // Guard against undefined containerId
    try {
      const data = await fetchContainerWithComponent(projectId, containerId);
      setContainerData(data);
      setComponentData(data?.data?.components);

      // Fetch functional requirements if ImplementedRequirementIDs exists
      if (data?.data?.container?.properties?.ImplementedRequirementIDs) {
        const requirements = await getContainerFunctionalRequirements(
          projectId,
          containerId
        );
        setFunctionalRequirements(requirements);
      }
    } catch (err) {

    }
  };

  const fetchContainers = async () => {
    try {
      const data = await fetchSystemContextWithContainers(projectId);
      if (data?.data?.containers) {
        const storedSkips = JSON.parse(sessionStorage.getItem('skippedContainers') || '[]');
        const filteredContainers = data.data.containers.filter(
          (container) => !storedSkips.includes(container.id)
        );

        // Transform container data to a simpler format
        const formattedContainers = filteredContainers.map(container => ({
          id: container.id,
          title: container.properties.Title || 'Untitled Container',
          description: container.properties.Description || 'No description available',
          containerType: container.properties.ContainerType || 'unknown',
          type: container.properties.Type || 'Container',
          properties: container.properties,
          ui_metadata: container.ui_metadata
        })).sort((a, b) => {
          if (a.containerType?.toLowerCase() === "internal" && b.containerType?.toLowerCase() !== "internal") {
            return -1;
          }
          if (b.containerType?.toLowerCase() === "internal" && a.containerType?.toLowerCase() !== "internal") {
            return 1;
          }
          return 0;
        });

        setSkippedContainers(storedSkips);
        setContainers(formattedContainers);
        setContainerCount(data.data.containers.length);
      }
    } catch (error) {

      showAlert('Failed to load containers', 'error');
    } finally {
      setIsLoading(false)
    }
  };
  const handlePropertyUpdate = async (key, value) => {
    try {
      const response = await updateNodeByPriority(currentContainer?.id, key, value);

      if (response === "success" || response?.status === "success") {
        setContainerData((prev) => ({
          ...prev,
          data: {
            ...prev.data,
            container: {
              ...prev.data.container,
              properties: {
                ...prev.data.container.properties,
                [key]: value,
              },
            },
          },
        }));

        showAlert("Content updated successfully", "success");
      } else {
        showAlert("Failed to update content", "error");
      }
    } catch (error){
     
      showAlert("Failed to update content", "error");
    }
  };


  const headers = [
    { key: "id", label: "Id" },
    { key: "title", label: "Title" },
    // { key: "type", label: "Type" },
    { key: "description", label: "Description" },
  ];

  const requirementHeaders = [
    { key: "id", label: "ID" },
    { key: "Title", label: "Title" },
    // { key: "Type", label: "Type" },
    { key: "Description", label: "Description" },
  ];

  const cleanDescription = (description) => {
    if (!description) return '';

    return description
      .replace(/#+\s/g, '')        // Remove markdown headers (# Header)
      .replace(/\*\*/g, '')        // Remove bold (**text**)
      .replace(/\*/g, '')          // Remove italics (*text*)
      .replace(/`/g, '')           // Remove code ticks (`code`)
      .replace(/\n\n/g, ' ')       // Replace double line breaks with space
      .replace(/\n-\s/g, ', ')     // Replace bullet points with commas
      .replace(/\n\d+\.\s/g, ', ') // Replace numbered lists with commas
      .replace(/\n/g, ' ')         // Replace remaining line breaks with spaces
      .replace(/\s{2,}/g, ' ')     // Replace multiple spaces with single space
      .trim();                     // Trim extra whitespace
  };


  const requirementTableData = functionalRequirements?.functional_requirements?.map((req) => ({
    id: req.id,
    Title: req.properties.Title,
    // Type: <span className="bg-gray-100 rounded-3xl px-3 typography-caption p-1 font-weight-medium">
    //   {req.properties.Type}
    // </span>,
    Description: req.properties.Description,
  }));

  const tableData = ContainerData?.data?.components.map((data) => ({
    id: data.id,
    title: data.properties.Title,
    // type: data.properties.Type,
    description: cleanDescription(data.properties.Description),
  }));

  useEffect(() => {
    if (!projectId && !containerId) return; // Guard against undefined projectId
    fetchData(containerId);
  }, [projectId, containerId, searchParams]);

  useEffect(() => {
    if (projectId) {
      fetchContainers();
    }
  }, [projectId]);


  useEffect(() => {
    if (selectedContainerId && containers.length > 0) {
      // Find the index of the selected container
      const index = containers.findIndex(container => container.id.toString() === selectedContainerId);
      if (index !== -1) {
        setCurrentIndex(index);
        setContainerId(containers[index].id);
      }
    }
  }, [selectedContainerId, containers]);

  useEffect(() => {
    if (configStatus[currentTaskId]) {

      setTaskStatus(configStatus[currentTaskId].task_status || "Idle");
      setAutoNavigateEnabled(false)

    }
  }, [currentTaskId, configStatus[currentTaskId], projectId])

  useEffect(() => {
    const fetchContainerOnComplete = async () => {
      if (taskStatus.toLowerCase() === "complete" && projectId) {
        await fetchContainers();
      }
    };

    fetchContainerOnComplete();
  }, [taskStatus, projectId])


  const handleCloseModal = () => {
    setConfigureModel(false);
  };

  const handleConfigureClick = () => {
    setConfigMethod('auto');
    setConfigureModel(true);
  };

  const handleUpdateContainer = (id) => {
    setConfigMethod('discussion');
    if (!id) return;
    setContainerId(id);

    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", id);
    newSearchParams.set("node_type", "Container");
    newSearchParams.set("is_creating_Container", "true");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleSkipAll = () => {
    if (containers.length === 0) return;

    const allIdsToSkip = containers.map((container) => container.id);
    const updatedSkipped = [...skippedContainers, ...allIdsToSkip];
    sessionStorage.setItem('skippedContainers', JSON.stringify(updatedSkipped));
    setSkippedContainers(updatedSkipped);
    setShowAllSkippedNotification(true);

    setCurrentIndex(containers.length - 1);
    setTimeout(() => {
      setContainers([]);
      setCurrentIndex(0);
    }, 50);

    // Log skipped containers to MongoDB - updated for new backend structure
    try {
      createProjectGuidanceFlow(parseInt(projectId), {
        project_id: parseInt(projectId),
        step_name: "containers_skipped",
        status: "completed",
        data: {
          skipped_containers: allIdsToSkip.map((id) => parseInt(id)),
          type: "Container",
          status: "skipped",
        },
      })
        .then((result) => {

        })
        .catch((error) => {

        });
    } catch (error) {

    }
  };

  const handleSkip = () => {
    if (!containers[currentIndex]) return;

    const isContainerConfigured = sessionStorage.getItem(
      `openContainerContent-${projectId}-${containers[currentIndex].id}`
    ) === 'true';

    if (isContainerConfigured) {
      // Just move to the next container when already configured
      setCurrentIndex((prev) => Math.min(prev + 1, containers.length - 1));
    } else {
      const currentContainerId = containers[currentIndex].id;
      const updatedSkipped = [...skippedContainers, currentContainerId];
      sessionStorage.setItem('skippedContainers', JSON.stringify(updatedSkipped));
      setSkippedContainers(updatedSkipped);
      setShowSkipNotification(true);

      // Log the skipped container to MongoDB - updated for new backend structure
      try {
        createProjectGuidanceFlow(parseInt(projectId), {
          project_id: parseInt(projectId),
          step_name: "container_skipped",
          status: "completed",
          data: {
            container_id: parseInt(currentContainerId),
            type: "Container",
            status: "skipped",
          },
        })
          .then((result) => {

          })
          .catch((error) => {

          });
      } catch (error) {

      }

      setTimeout(() => {
        const newContainers = containers.filter((container) => container.id !== currentContainerId);
        setContainers(newContainers);
        // Adjust current index if needed
        if (currentIndex >= newContainers.length) {
          setCurrentIndex(Math.max(0, newContainers.length - 1));
        }
        setShowSkipNotification(false);
      }, 5000);
    }
  };

  const handlePrevious = () => {
    setCurrentIndex((prev) => Math.max(0, prev - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => Math.min(prev + 1, containers.length - 1));
  };

  const currentContainer = containers[currentIndex];

  // Update containerId when currentContainer changes
  useEffect(() => {
    if (currentContainer?.id) {
      setContainerId(currentContainer.id);
    }
  }, [currentContainer, setContainerId]);

  const isFirstContainer = currentIndex === 0;
  const isLastContainer = currentIndex === containers.length - 1 || containers.length === 0;

  // Check if current container is configured
  const isContainerConfigured =
    // Check ContainerData if available
    (ContainerData?.data?.container?.properties?.configuration_state === "configured") ||
    // Or check session storage
    (currentContainer && sessionStorage.getItem(`openContainerContent-${projectId}-${currentContainer.id}`) === 'true');

  // Notification components
  function ContainerSkippedNotification() {
    return (
      <div className="max-w-3xl mx-auto p-4">
        <div className="border border-cyan-200 bg-cyan-50 rounded-lg p-4 flex items-start gap-3">
          <div className="text-gray-500">
            <Info size={24} />
          </div>
          <div>
            <p className="text-gray-800 font-weight-medium">
              Container skipped: You can configure {currentContainer?.title} later in the workflow manager.
            </p>
          </div>
        </div>
      </div>
    );
  }

  function LoadingSkeleton() {

    return (

      <div className="bg-white rounded-lg shadow-md p-6 mb-8 animate-pulse">

        <div className="flex justify-between items-center mb-4">

          <div className="h-6 w-24 bg-gray-200 rounded"></div>

          <div className="h-8 w-20 bg-gray-200 rounded"></div>

        </div>

        <div className="h-8 w-3/4 bg-gray-200 rounded mb-4"></div>

        <div className="flex space-x-2 mb-6">

          <div className="h-6 w-16 bg-gray-200 rounded-full"></div>

          <div className="h-6 w-20 bg-gray-200 rounded-full"></div>

        </div>

        <div className="mb-6">

          <div className="h-6 w-1/2 bg-gray-200 rounded mb-4"></div>

          <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>

          <div className="h-4 w-5/6 bg-gray-200 rounded"></div>

        </div>

        <div className="flex flex-col md:flex-row gap-6 justify-center md:justify-start">

          <div className="w-[300px] h-[200px] bg-gray-100 rounded-lg p-6">

            <div className="flex items-center space-x-2 mb-4">

              <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>

              <div className="h-6 w-32 bg-gray-200 rounded"></div>

            </div>

            <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>

            <div className="h-4 w-5/6 bg-gray-200 rounded mb-2"></div>

            <div className="h-4 w-4/6 bg-gray-200 rounded"></div>

          </div>

          <div className="w-[300px] h-[200px] bg-gray-100 rounded-lg p-6">

            <div className="flex items-center space-x-2 mb-4">

              <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>

              <div className="h-6 w-32 bg-gray-200 rounded"></div>

            </div>

            <div className="h-4 w-full bg-gray-200 rounded mb-2"></div>

            <div className="h-4 w-5/6 bg-gray-200 rounded mb-2"></div>

            <div className="h-4 w-4/6 bg-gray-200 rounded"></div>

          </div>

        </div>

      </div>

    );

  }

  function AllContainersSkippedNotification() {
    return (
      <div className="border-l-4 border-green-500 bg-green-50 p-4 mb-8 flex items-start gap-3">
        <div className="text-gray-500">
          <Info size={24} />
        </div>
        <div>
          <p className="text-gray-800">
            All containers skipped: You can configure all containers later in the workflow manager.
          </p>
        </div>
      </div>
    );
  }

  function SuccessBadge() {
    return (
      <div>
        {ContainerData ? (
          <div>
            <PropertiesRenderer
              properties={ContainerData?.data?.container?.properties}
              metadata={ContainerData?.model?.Container?.ui_metadata}
              to_skip={[
                "configuration_state",
                "Type",
                "Title",
                "ImplementedRequirementIDs",
              ]}
              onUpdate={handlePropertyUpdate}
            />


            <div id="system-components" className="relatedContentDiv ">
              {ContainerData?.data?.components?.length > 0 ? (
                <>
                  <TableComponent
                    data={tableData}
                    onRowClick={(id) => {
                      const newSearchParams = new URLSearchParams(searchParams);
                      newSearchParams.set("stepName", "Components Details");
                      newSearchParams.set("selectedComponentId", id);
                      router.push(`${pathname}?${newSearchParams.toString()}`);
                    }}
                    headers={headers}
                    sortableColumns={{ id: true, title: true, type: true }}
                    itemsPerPage={20}
                    title={en.ChildComponentsHeading}
                  />
                </>
              ) : (
                <div className="mb-4">
                  <PlaceholderMessage type="components" message="No components are currently available" subMessage="Add a new  components to get started." />
                </div>
              )}
            </div>
            <div id="implemented-functional-requirements" className="relatedContentDiv mt-3">
              {functionalRequirements && (
                <div className="relatedContentDiv mt-3">
                  <TableComponent
                    data={requirementTableData}
                    headers={requirementHeaders}
                    sortableColumns={{ Title: true, Type: true }}
                    itemsPerPage={20}
                    title="Implemented Functional Requirements"
                    onRowClick={() => { }}
                  />
                </div>
              )}
              <div id="related-interfaces">
                {ContainerData?.data?.interfaces?.length > 0 ? (
                  <TableComponent
                    data={ContainerData.data.interfaces.map((interfaces) => ({
                      id: interfaces.id,
                      title: interfaces.properties.Title,
                      type: interfaces.type,
                      description: interfaces.properties.Description,
                    }))}
                    onRowClick={(id) => { }
                    }
                    headers={headers}
                    sortableColumns={{ id: true, title: true, type: true }}
                    itemsPerPage={20}
                    title={en.InterfacesHeading}
                  />
                ) : (
                  <div className="mb-3">
                    <PlaceholderMessage type="interfaces" message="No interfaces are currently available" subMessage="Add a new  interfaces to get started." />
                  </div>
                )}
              </div>
            </div>
          </div>) : (<p className="notFound">
            <EmptyStateView type="containerDetails" />
          </p>)}
      </div>);
  }

  return (
    <div className="p-6 px-4 bg-white">
      {showConfigModel && (taskStatus.toLowerCase() === 'in_progress' || taskStatus.toLowerCase() === 'idle') ? (
        <StatusPanel />
      ) : (
        <>
          <div className="mx-auto w-full">

            {/* Skip Button and Navigation Controls */}
            <div className="flex justify-end mb-2 -mt-6">
              <div className="flex space-x-2">
                <button
                  className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200"
                  onClick={handleBackToContainers}
                  title="Click to see the containers list"
                >
                  <ArrowLeft size={16} className="mr-2" />
                  Back to Containers List
                </button>

                <button
                  className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200 ${isFirstContainer ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={handlePrevious}
                  disabled={isFirstContainer}
                  title='Click to see previous container'
                >
                  <ArrowLeft size={16} className="mr-1" />
                  Previous
                </button>
                <button
                  className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-gray-100 transition border border-gray-200 ${isLastContainer ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={handleNext}
                  disabled={isLastContainer}
                  title='Click to see next container'
                >
                  Next
                  <ArrowRight size={16} className="ml-1" />
                </button>
              </div>
              {/* <button
            className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-orange-100 hover:text-orange-700 transition border border-gray-200"
            onClick={handleSkipAll}
            disabled={containers.length === 0}
          >
            <FaForward size={16} className="mr-1" />
            Skip All
          </button> */}
            </div>

            {showAllSkippedNotification && <AllContainersSkippedNotification />}

            {/* Container Progress */}
            {!showAllSkippedNotification && (
              <div className="flex justify-between items-center mb-6 px-4 py-2 bg-gray-100 rounded">
                <div className="text-gray-700 font-weight-medium">Container Progress:</div>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-700">{currentIndex + 1} / {containerCount}</span>
                  <div className="w-24 h-2 bg-gray-200 rounded-full">
                    <div
                      className="h-2 bg-orange-500 rounded-full"
                      style={{ width: `${((currentIndex + 1) / containerCount) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            )}

            {/* Container Card */}
            {
              isLoading ? (

                <LoadingSkeleton />) :
                currentContainer ? (
                  <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                    <div className="flex justify-between items-center mb-4 space-x-4">
                      <div className='flex items-center space-x-4 flex-wrap'>
                        <div className="typography-body-sm bg-gray-100 text-gray-500 px-3 py-1 rounded">
                          {`CONTAINER-${currentContainer.id || 'ID'}`}
                        </div>
                        <h2 className="typography-body-lg font-weight-semibold text-gray-800 ">
                          {currentContainer.title}
                        </h2>
                        <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full typography-body-sm">
                          {currentContainer.containerType || 'Container'}
                        </span>
                        <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full typography-body-sm">
                          {currentContainer.type}
                        </span>
                      </div>
                      {/* <button
                className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-orange-100 hover:text-orange-700 transition border border-gray-200 ${currentContainer?.properties?.configuration_state === "configured"? "disabled:opacity-50 disabled:cursor-not-allowed":""}`}
                onClick={handleSkip}
                disabled={currentContainer?.properties?.configuration_state === "configured"}
              >
                <FaForward size={16} className="mr-1" />
                { 'Skip'}
              </button> */}
                    </div>



                    <div className="flex space-x-2 mb-6">

                    </div>

                    {showSkipNotification ? (
                      <ContainerSkippedNotification />
                    ) : isContainerConfigured ? (
                      <SuccessBadge />
                    ) : (
                      <>
                        <div className="mb-6">
                          <h3 className="typography-body-lg font-weight-medium text-gray-700 mb-4">
                            Configure This Container
                          </h3>
                          {/* <p className="text-gray-600">{currentContainer.description}</p> */}
                        </div>

                        {/* Configuration Buttons */}
                        <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
                          <div
                            className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === 'discussion' ? 'border-orange-500 bg-orange-50' : 'border-gray-200'
                              }`}
                            onClick={() => handleUpdateContainer(currentContainer.id)}
                          >
                            <div className="flex items-center space-x-2 mb-4">
                              <div className="py-1 px-1.5 bg-orange-100 rounded-lg flex items-center justify-center">
                                <Image
                                  src={Logo}
                                  alt="Logo"
                                  width={16}
                                  height={16}
                                  className="text-orange-500"
                                />
                              </div>
                              <h4 className="typography-body-lg font-weight-medium">Interactive configuration</h4>
                            </div>

                            <p className="text-gray-600">
                              {en.ContainerUpdate}
                            </p>
                          </div>

                          <div
                            className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === 'auto' ? 'border-orange-500 bg-orange-50' : 'border-gray-200'
                              }`}
                            onClick={handleConfigureClick}
                          >
                            <div className="flex mb-4 items-center space-x-2">
                              <div className="p-1.5 bg-orange-100 rounded-lg flex items-center justify-center">
                                <Upload className="w-4 h-4 text-orange-500" />
                              </div>
                              <h4 className="typography-body-lg font-weight-medium">Auto Configuration</h4>
                            </div>

                            <p className="text-gray-600">
                              Let our LLM automatically configure this container based on the available information
                            </p>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                ) : !showAllSkippedNotification ? (
                  <p className="text-center text-gray-500">Loading containers...</p>
                ) : null}
          </div>
        </>)}

      {configureModel && currentContainer && (
        <ConfigureModal
          id={currentContainer.id}
          type={"Architecture"}
          isNodeType={"Architecture"}
          isCreateProject={true}
          setShowConfigModel={setShowConfigModel}
          requirementId={currentContainer.id}
          closeModal={handleCloseModal}
          setLoadingAutoConfigure={setLoadingAutoConfigure}
          onSubmitSuccess={() => {
            const successMessage = "Container Configured Successfully";
            showAlert(successMessage, "success");

            // Log successful configuration to MongoDB - updated for new backend structure
            try {
              createProjectGuidanceFlow(parseInt(projectId), {
                project_id: parseInt(projectId),
                step_name: "container_configuration",
                status: "completed",
                data: {
                  container_id: parseInt(currentContainer.id),
                  type: "Container",
                  status: "configured",
                },
              })
                .then((result) => {

                  // Set the flag to indicate this container has been configured
                  sessionStorage.setItem(`openContainerContent-${projectId}-${currentContainer.id}`, "true");
                  // Refresh the containers list
                  fetchContainers();
                })
                .catch((error) => {

                });
            } catch (error) {

            }
          }}
        />
      )}
    </div>
  );
}
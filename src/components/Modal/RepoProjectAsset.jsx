// RepoProjectAsset.jsx - Updated to use separate ManifestUpdateModal component

"use client";

import React, { useContext, useEffect, useState } from "react";
import {
  RefreshCw,
  Search,
  Code,
  GitBranch,
  Clock,
  Plus,
  ChevronDown,
  ChevronUp,
  Trash2,
  AlertCircle,
  Check,
  X,
  FileText
} from "lucide-react";
import { usePathname } from "next/navigation";
import { getkginfo, repoSynchronization } from "@/utils/gitAPI";
import EmptyStateView from "./EmptyStateModal";
import Pagination from "../UIComponents/Paginations/Pagination";
import { AlertContext } from "../NotificationAlertService/AlertList";
import { useProjectAsset } from "../Context/ProjectAssetContext";
import RepositoryAddition from "../Repository/RepositoryAddition";
import { getScmConfiguration } from '@/utils/api';
import { formatListDate } from "@/utils/datetime";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { useBuildProgress } from "../Context/BuildProgressContext";
import { configureNodeWithAgent, deleteRepoBranch } from "@/utils/api";
import ExtractionProgressModal from "./ExtractionProgressModal";
import { ExecutionContext } from "../Context/ExecutionContext";
import WebSocketSessionsComponent from "../WebSocket/WebSocketSessionsComponent";
import { updateSelectedBranch } from "@/utils/gitAPI";
import { useWebSocket } from "@/components/Context/WebsocketContext";

// Import the new separate ManifestUpdateModal component
import ManifestUpdateModal from "./ManifestUploadModal";

const DeleteRepoModal = ({onDelete, onClose}) => {
    const [isDeleting, setIsDeleting] = useState(false);

    const handleDelete = async () => {
      setIsDeleting(true);
      await onDelete();
      onClose();
    }

    return(
        <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
          <div
            className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
            onClick={onClose}
          />
          <div className="relative bg-white rounded-lg shadow-lg w-full max-w-md mx-4 p-4">
            <div className="flex items-center justify-between border-b pb-2">
              <h2 className="typography-body-lg font-weight-semibold text-gray-900">Delete Repository</h2>
              <button
                className="text-gray-400 hover:text-gray-600 rounded-md p-1 hover:bg-gray-100 transition-colors"
                onClick={onClose}
                aria-label="Close"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="py-4 space-y-2">
              <p className="text-gray-600 typography-body-sm">
                Are you sure you want to delete this repository from Kavia?
              </p>
              <div className="flex items-start p-2 bg-red-50 rounded-md text-red-800">
                <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
                <p className="typography-body-sm">
                  By deleting this repository, other related data will also be permanently removed
                </p>
              </div>
            </div>
            <div className="flex justify-end gap-3 pt-2 border-t">
              <button
                className="px-4 py-2 typography-body-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                onClick={onClose}
                aria-label="Cancel delete"
              >
                Cancel
              </button>
              <button
                className={`px-4 py-2 typography-body-sm text-white rounded-md ${isDeleting ? "bg-red-300" :"bg-red-600  hover:bg-red-700"} transition-colors`}
                onClick={handleDelete}
                aria-label="Confirm delete"
                disabled={isDeleting}
              >
                <span>{isDeleting? "Deleting..." : "Delete"}</span>
              </button>
            </div>
          </div>
        </div>
    )
}

const RepositoryList = ({
  isRepoModalOpen,
  setIsRepoModalOpen,
  setCodeLength,
  setActiveTab,
  setTabCounts,
  handleCloseAsset
}) => {

  const { getBuildProgress, initiateBuildStatus, buildStatus, clearBuildProgress, buildProgress, setBuildProgress } = useBuildProgress();
  const { connectToSession } = useWebSocket();
  const [kgResponse, setKgResponse] = useState({})
  const [repositories, setRepositories] = useState([]);
  const pathname = usePathname();
  const pathParts = pathname.split("/");
  const projectId = pathParts[3];
  const [isLoading, setIsLoading] = useState(true);
  const [extractLoad, setExtractLoad] = useState(false)
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  const [selectedSCMType, setSelectedSCMType] = useState(null);
  const [scmConfigurations, setSCMConfigurations] = useState([]);
  const [step, setStep] = useState('initial');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRepositories, setSelectedRepositores] = useState([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState({ filesProcessed: 0, totalFiles: 0 });
  const [addBtnClicked, setAddBtnClicked] = useState(false)
  const { showAlert } = useContext(AlertContext);
  const { rawWebSocketMessage } = useProjectAsset();
  const [showRepos, setShowRepos] = useState(false)
  const [buttonEnable, setButtonEnable] = useState(false)
  const [showExtractionModal, setShowExtractionModal] = useState(false);
  const [deleteRepoModalId, setDeleteRepoModalId] = useState(null);
  const [showManifestModal, setShowManifestModal] = useState(null);
  const {refreshRepos, setRefreshRepos} = useWebSocket();

  const [ selectedManifestData, setSelectedManifestData] = useState(null);
  const [openDropdowns, setOpenDropdowns] = useState({});

  const { setCurrentTaskId, setConfiglabel, taskStatusUpdate, extractTaskId, setExtractTaskId, currentTaskId, extract, setExtract } = useContext(ExecutionContext)
  const [taskStatus, setTaskStatus] = useState(false)

  const toggleDropdown = (buildId) => {
    setOpenDropdowns(prev => ({
      ...prev,
      [buildId]: !prev[buildId]
    }));
  };

  const openDropdown = (buildId) => {
    setOpenDropdowns(prev => ({
      ...prev,
      [buildId]: true
    }));
  }

  const updateBuildStatuses = (buildIds, type="clone") => {
    initiateBuildStatus(buildIds, type)
  }

  const changeRepoStatus = (repoId, branchName, status) => {
    setRepositories((prev) => {
      const newRepositories = prev.map((repo) =>
        repo.id === repoId
          ? {
            ...repo,
            status: repo.selectedBranch === branchName ? status : repo.status,
            branches: repo.branches.map((b) =>
              b.name === branchName && (status === "In Progress" || status === "Rebuilding")
                ? { ...b, upstream: false }
                : b
            ),
          }
          : repo
      );
      return newRepositories;
    })
  }

  const handleSync = async (projectId, repoId, branchName, buildId, status) => {
    let statusCode = 3;
    let changedStatus = "In Progress";
    if(status === "Completed"){
      statusCode = 2;
      changedStatus = "Rebuilding";
    }
      try {
        changeRepoStatus(repoId, branchName, changedStatus);
        setOpenDropdowns(prev => ({
          ...prev,
          [buildId]: true
        }));
        const response = await repoSynchronization(projectId, buildId, statusCode);
        if(response.build_session_id){
          updateBuildStatuses([buildId], "upstream");
          connectToSession(response.build_session_id);
        }
      } catch (error) {
        
      } finally {
        fetchRepositoryData(false);
        // Trigger refresh for other components
        setRefreshRepos(true);
      }
  };

  const getBuildID = (repoId, repoService) => {
    const currentRepo = repositories.find((repo) => repo.id === repoId);
    if(repoService == "localFiles"){
      return currentRepo.builds.buildId;
    }
    else {
      const currentBranch = currentRepo?.branches.find(
        (branch) => branch.name === currentRepo?.selectedBranch
      );
      return currentBranch?.buildId;
    }
  };

  const checkRepoSelected = (repoId) => {
    if (selectedRepositories.find((repo) => repo.id === repoId)) {
      return true;
    }
    else {
      return false;
    }
  }

  const toggleRepoSelect = (repo) => {
    if (checkRepoSelected(repo.id)) {
      setSelectedRepositores((prev) => prev.filter((repository) => repository.id != repo.id));
    }
    else {
      setSelectedRepositores((prev) => [...prev, repo]);
    }
  }

  const fetchRepositoryData = async (showLoading = true) => {
    try {
      if(showLoading){
        setIsLoading(true);
      }
      const response = await getkginfo(projectId, true);
      setKgResponse(response)
      if (response.details && response.details.length > 0) {
        const repoData = response.details.map((repo) => {
          if(repo.service == "github"){
            const firstBranch = repo.selected_branch ? repo.branches.find((branch) => branch.name == repo.selected_branch) : repo.branches[0];
            const firstBranchStatus = repo.selected_branch?.builds?.kg_creation_status || firstBranch?.builds?.kg_creation_status;
            if(firstBranchStatus == 1){
              openDropdown(firstBranch.builds.build_id);
            }
            return {
              //id: repo.git_url.split("/").pop().split(".")[0],
              id: repo.repo_id,
              name: repo.git_url.split("/").pop().split(".")[0],
              service: repo.service,
              gitUrl: repo.git_url.replace(".git", ""),
              repoType: repo.repo_type,
              branches: repo.branches.map((branch) => ({
                name: branch.name,
                buildId: branch.builds.build_id,
                upstream: branch.upstream,
                kg_creation_status: branch.builds.kg_creation_status,
                pr_creation_status: branch.builds.pr_created,
                pr_details: branch.builds.pr_details,
                status:
                  branch.builds.kg_creation_status === 0
                    ? "Not Started"
                    : branch.builds.kg_creation_status === 1
                      ? "In Progress"
                      : branch.builds.kg_creation_status === 2
                        ? "Completed"
                        : branch.builds.kg_creation_status === 3
                          ? "Needs Rebuild"
                          : branch.builds.kg_creation_status === 4
                          ? "Rebuilding"
                          : "Failed",
                lastUpdated: branch.builds.build_info.last_updated,
                type:
                  branch.name === "main" || branch.name === "master"
                    ? "main"
                    : branch.name.startsWith("feature/")
                      ? "feature"
                      : branch.name.startsWith("fix/")
                        ? "fix"
                        : branch.name.startsWith("dev")
                          ? "development"
                          : "other",
                upstream: branch.builds.kg_creation_status <= 1 ? false : branch.upstream
              })),
              selectedBranch: repo.selected_branch || firstBranch?.name || "main",
              status:
                firstBranchStatus === 0
                  ? "Not Started"
                  : firstBranchStatus === 1
                    ? "In Progress"
                    : firstBranchStatus === 2
                      ? "Completed"
                      : firstBranchStatus === 3
                        ? "Needs Rebuild"
                        : firstBranchStatus === 4
                          ? "Rebuilding"
                          : "Failed",
              created_at: response.created_at,
            };
          }
          else {
            return {
              id: repo.repo_id,
              name: repo.repository_name,
              service: repo.service,
              repoType: "files",
              builds: {
                buildId: repo.builds.build_id,
                kg_creation_status: repo.builds.kg_creation_status,
                status:
                  repo.builds.kg_creation_status === 0
                    ? "Not Started"
                    : repo.builds.kg_creation_status === 1
                      ? "In Progress"
                      : repo.builds.kg_creation_status === 2
                        ? "Completed"
                        : repo.builds.kg_creation_status === 3
                          ? "Needs Rebuild"
                          : repo.builds.kg_creation_status === 4
                          ? "Rebuilding"
                          : "Failed",
                lastUpdated: repo.builds.build_info.last_updated,
              },
              status:
                repo.builds.kg_creation_status === 0
                ? "Not Started"
                : repo.builds.kg_creation_status === 1
                  ? "In Progress"
                  : repo.builds.kg_creation_status === 2
                    ? "Completed"
                    : repo.builds.kg_creation_status === 3
                      ? "Needs Rebuild"
                      : repo.builds.kg_creation_status === 4
                      ? "Rebuilding"
                      : "Failed",
              created_at: response.created_at,
            }
          }
        });

        setRepositories(repoData);
        setTabCounts(prev => ({ ...prev, code: repoData.length }));
        setCodeLength(repoData.length);
      }
      else {
        if(response.detail == "No repository is found"){
          setRepositories([]);
          setTabCounts(prev => ({ ...prev, code: 0 }));
        }
      }
    } catch (error) {
      setRepositories([]);
      
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if(refreshRepos) {
      fetchRepositoryData(false);
      setRefreshRepos(false);
    }
  }, [refreshRepos])

  const handleCallApi = async () => {
    await fetchRepositoryData()
    // Trigger refresh for other components
    setRefreshRepos(true);
  }

  const handleRemoveRepository = async (buildId, repoService) => {
    try {
      const response = await deleteRepoBranch(String(buildId), projectId, repoService);
      if (response) {
        showAlert("Successfully deleted branch.", "success");
        fetchRepositoryData();
        // Trigger refresh for other components
        setRefreshRepos(true);
      } else {
        throw new Error("Failed to delete branch");
      }
    } catch (error) {
      showAlert("Failed to delete branch", "error");
      
    }
  };

  const handleBuildComplete = (repoId, repoService, branchName) => {

    if (!repositories || repositories.length === 0 || !repoId) return;
    if(repoService == "localFiles"){
      setRepositories((prev) => {
        const newRepositories = prev.map((repo) =>
          repo.id === repoId ?
          {
            ...repo,
            builds: {...repo.builds, kg_creation_status: 2, status: "Completed"},
            status: "Completed"
          } : repo
        );

        if (JSON.stringify(newRepositories) === JSON.stringify(prev)) {
          return prev;
        }

        const buildId = repositories.filter((repo) => repo.id === repoId)[0].builds.buildId;

        clearBuildProgress(buildId);

        return newRepositories;
      })
    }
    else {
      setRepositories((prev) => {
        const newRepositories = prev.map((repo) =>
          repo.id === repoId
            ? {
              ...repo,
              status: repo.selectedBranch === branchName ? "Completed" : repo.status,
              branches: repo.branches.map((b) =>
                b.name === branchName
                  ? { ...b, builds: { ...b.builds, kg_creation_status: 2 } }
                  : b
              ),
            }
            : repo
        );

        if (JSON.stringify(newRepositories) === JSON.stringify(prev)) {
          return prev;
        }

        const buildId = repositories.filter((repo) => repo.id === repoId)[0].branches.filter((branch) => branch.name === branchName)[0].buildId;

        clearBuildProgress(buildId);

        return newRepositories;
      });
    }
  };

  useEffect(() => {
    filteredRepositories.forEach((repo) => {
      const buildId = getBuildID(repo.id, repo.service);
      const percentageComplete = buildProgress[buildId]?.percentageComplete || 0;
      const currentBranch = repo.service == "localFiles" ? "localBranch": repo.branches.find((branch) => branch.name === repo.selectedBranch) ;

      if (percentageComplete === 100) {
        handleBuildComplete(repo.id, repo.service, currentBranch.name);
      }
    });
  }, [buildProgress]);

  useEffect(() => {
    fetchRepositoryData();
  }, []);

  useEffect(() => {
    fetchSCMConfigurations();
  }, []);

  const fetchSCMConfigurations = async () => {
    try {
      setLoading(true);
      const response = await getScmConfiguration();
      if (response.status === "success") {
        setSCMConfigurations(response.data?.configurations || []);
      }
    } catch (err) {
      
      setError('Failed to fetch SCM configurations');
    } finally {
      setLoading(false);
    }
  };

  const handleSCMTypeSelect = (scmType) => {
    setSelectedSCMType(scmType);
    setStep('select-scm');
  };


  const handleAddRepoClick = () => {
    setIsRepoModalOpen(true);
    setAddBtnClicked(true)
  };

  const handleAddRepoClose = () => {
    setIsRepoModalOpen(false);
    setAddBtnClicked(false);
    // Trigger refresh for other components when modal is closed
    setRefreshRepos(true);
  }

  const filteredRepositories = repositories.filter((repo) =>
    repo.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getCurrentPageItems = () => {
    const items = searchTerm ? filteredRepositories : repositories;
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return items.slice(startIndex, endIndex);
  };

  const handleNotImplemented = () => {
    showAlert("Functionality was not implemented", "info");
  };

  useEffect(() => {
    if (rawWebSocketMessage?.data?.progress_by_codebase) {
      const progressData = rawWebSocketMessage.data.progress_by_codebase;
      const updatedProgress = Object.entries(progressData).reduce((acc, [buildId, data]) => ({
        ...acc,
        [buildId]: {
          filesProcessed: data.files_processed,
          filesReady: data.files_ready,
          percentageComplete: data.percentage_complete,
          totalFiles: data.total_files,
          estimatedTime: data.estimated_time_remaining
        }
      }), {});

      setBuildProgress(prev => ({
        ...prev,
        ...updatedProgress
      }));
    }
  }, [rawWebSocketMessage]);

  const BuildProgressLoader = ({statusData}) => (
    <div>
      <div className="typography-body-sm">
          <p className="mb-2">Status:</p>
          {statusData? (
            statusData.map((statusItem, index) => (
              <div className="flex gap-4 mb-2 items-center" key={`statusItem-${index}`}>
                {statusItem.status == "Processing" ?
                  <div className="w-4 h-4 rounded-full border-4 border-black/10 border-l-primary animate-spin ease-linear" /> :
                  statusItem.status == "Success" ?
                  <div className="rounded-full w-4 h-4 bg-green-500 border-none flex items-center justify-center">
                    <Check className="w-2.5 h-2.5 text-white" />
                  </div> :
                  <div className="rounded-full w-4 h-4 bg-red-500 border-none flex items-center justify-center">
                    <X className="w-2.5 h-2.5 text-white" />
                  </div>
                }
                <p>{statusItem.message}</p>
              </div>
            ))
          ): (
            <p>Getting Status...</p>
          )}
        </div>
    </div>
  )


  const DocumentLoader = () => {
    return (
      <div className="border rounded-lg p-4 animate-pulse">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Document icon skeleton */}
            <div className="w-6 h-6 bg-gray-200 rounded"></div>

            <div>
              {/* Filename skeleton */}
              <div className="h-5 w-48 bg-gray-200 rounded mb-2"></div>

              {/* Info line skeleton */}
              <div className="flex items-center gap-2">
                <div className="h-4 w-12 bg-gray-200 rounded"></div>
                <div className="h-4 w-4 bg-gray-200 rounded"></div>
                <div className="h-4 w-16 bg-gray-200 rounded"></div>
                <div className="h-4 w-4 bg-gray-200 rounded"></div>
                <div className="h-4 w-24 bg-gray-200 rounded"></div>
                <div className="h-4 w-16 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>

          {/* Action buttons skeleton */}
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 bg-gray-200 rounded"></div>
            <div className="w-5 h-5 bg-gray-200 rounded"></div>
          </div>
        </div>

        {/* Progress bar skeleton */}
        <div className="mt-3">
          <div className="w-full bg-gray-200 rounded-full h-2"></div>
        </div>
      </div>
    );
  };

  const handleBranchChange = async (e, repoId) => {
    const branchName = e.target.value;
    const current_branch = repositories.find((repo) => repo.id === repoId).selectedBranch;
    const currentRepo = repositories.find((repo) => repo.id === repoId);
    const buildId = currentRepo?.branches.find(
                      (branch) => branch.name === branchName
                    ).buildId;

    setRepositories((prev) =>
      prev.map((repo) =>
        repo.id === repoId
          ? {
            ...repo,
            selectedBranch: branchName,
            status:
              repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 0
                ? "Not Started"
                : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 1
                  ? "In Progress"
                  : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 2
                    ? "Completed"
                    : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 3
                      ? "Needs Rebuild"
                      : "Failed",
          }
          : repo
      )
    );

    const success = await updateSelectedBranch(Number(projectId), buildId, branchName);
    if(!success){
      showAlert("Failed to update the branch.", "error")
      setRepositories((prev) =>
        prev.map((repo) =>
          repo.id === repoId
            ? {
              ...repo,
              selectedBranch: current_branch,
              status:
                repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 0
                  ? "Not Started"
                  : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 1
                    ? "In Progress"
                    : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 2
                      ? "Completed"
                      : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 3
                        ? "Needs Rebuild"
                        : repo.branches.find((b) => b.name === branchName)?.kg_creation_status === 4
                          ? "Rebuilding"
                          : "Failed",
            }
            : repo
        )
      );
    } else {
      // Trigger refresh for other components when branch change is successful
      setRefreshRepos(true);
    }
  };

  useEffect(() => {
      repositories.forEach((repo) => {
        const buildId = getBuildID(repo.id, repo.service);
        const percentageComplete = buildProgress[buildId]?.percentageComplete || 0;

        if (percentageComplete === 100) {
          fetchRepositoryData();
        }
      });
  }, [buildProgress]);


  const ProcessingStatus = ({ buildId, processingStatus, onClose }) => {

    useEffect(() => {
      if(buildProgress[buildId]?.percentageComplete == 100){
        onClose();
      }
    }, [buildProgress])

    processingStatus = buildProgress[buildId];
    const statusData = buildStatus[buildId];

    return (
      <div className="space-y-4">
        <h2 className="typography-heading-4 font-weight-semibold">Processing Status</h2>
        <div className="space-y-4">
          {processingStatus ? (
            <div className="space-y-3">
              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex justify-between typography-body-sm">
                  <span>Progress</span>
                  <span>
                    {processingStatus?.percentageComplete?.toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="h-2 rounded-full bg-blue-500"
                    style={{
                      width: `${Math.min(processingStatus?.percentageComplete || 0, 100)}%`,
                    }}
                  />
                </div>
              </div>

              {/* File Details */}
              <div className="space-y-2">
                <div className="flex justify-between typography-body-sm">
                  <span>Total Files:</span>
                  <span>{processingStatus?.totalFiles || 0}</span>
                </div>
                <div className="flex justify-between typography-body-sm">
                  <span>Files Ready:</span>
                  <span>{processingStatus?.filesReady || 0}</span>
                </div>
                <div className="flex justify-between typography-body-sm">
                  <span>Files Processed:</span>
                  <span>{processingStatus?.filesProcessed || 0}</span>
                </div>

                <div className="flex justify-between typography-body-sm">
                  <span>Estimated Time1:</span>
                  <span>{processingStatus?.estimatedTime || 0}</span>
                </div>

                <div className="flex justify-between typography-body-sm">
                  <span>Build session id:</span>
                  <span><a
                    href={`https://us5.datadoghq.com/logs?query=discussion_id%3A${processingStatus?.buildSessionId || 0}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 underline"
                  >
                    <p title='view logs'>{processingStatus?.buildSessionId || 0}</p>
                  </a></span>
                </div>
              </div>
            </div>
          ) : (
            <BuildProgressLoader statusData={statusData}/>
          )}
        </div>
        <div className="flex justify-end pt-4">
          <button
            type="button"
            className="bg-gray-200 px-4 py-2 rounded hover:bg-gray-300"
            onClick={onClose}
          >
            Close
          </button>
        </div>
      </div>
    );
  };

  const DynamicProgressBar = ({ buildId, status, isDropdownOpen, onToggleDropdown }) => {
    const progress = getBuildProgress(buildId);
    const percentageComplete = progress?.percentageComplete;
    const estimatedTime = progress?.estimatedTime;

    // Remove the setButtonEnable call since it's not defined in this scope
    if (percentageComplete === 100) {
      status = "Completed";
    }

    let progressLabel = "Processing";
    let progressColor = "bg-primary-500";
    let adjustedPercentage = percentageComplete;

    if(status === "Rebuilding" || status == "In Progress"){
      progressLabel = status === "Rebuilding" ? "Rebuilding" : "Processing",
      progressColor = "bg-primary-500";
      adjustedPercentage = percentageComplete? percentageComplete : 0;
    }

    if (status === "Completed") {
      progressLabel = "Completed";
      progressColor = "bg-green-500";
      adjustedPercentage = 100;
    }

    if (status == "Needs Rebuild") {
      progressLabel = "Needs Rebuild";
      progressColor = "bg-gray-500";
      adjustedPercentage = 0;
    }

    if (status == "Failed") {
      progressLabel = "Failed";
      progressColor = "bg-red-500";
      adjustedPercentage = 0;
    }



    const showEstimatedTime = estimatedTime && percentageComplete > 0 && percentageComplete < 100;

    return (
      <>
        <div className="w-full mt-4">
          <div className="flex justify-between typography-body-sm text-gray-600 mb-1.5">
            <div className="flex items-center">
              <span
                className={`inline-block w-2 h-2 ${progressColor} rounded-full mr-2 ${(progressLabel === "Processing" || progressLabel==="Rebuilding") && "animate-pulse"
                  }`}
              />
              <span>{progressLabel}</span>
              {showEstimatedTime && (
                <div className={`relative ${status === "Completed" ? "hidden" : ""}`} title="time remaining to build repo">
                  <span className="animate-pulse inline-flex items-center gap-1 px-2 py-0.5 rounded-full typography-caption font-weight-medium bg-gray-100 text-gray-800">
                    <Clock className="w-3.5 h-3.5" />
                    Time left: {estimatedTime}
                  </span>
                </div>
              )}
            </div>
            <div className={`flex items-center gap-2 ${status === "Completed" ? "hidden" : ""}`}>
              {status !== "Needs Rebuild" && <span className="font-weight-medium">{adjustedPercentage?.toFixed(1)}%</span>}
              {(status === "In Progress" || status === "Rebuilding") && (
                <button
                  onClick={onToggleDropdown}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                  title="Toggle detailed view"
                >
                  {isDropdownOpen ? (
                    <ChevronUp className="w-4 h-4" />
                  ) : (
                    <ChevronDown className="w-4 h-4" />
                  )}
                </button>
              )}
            </div>
          </div>

          {/* Hide progress bar when dropdown is open */}
          {!isDropdownOpen && status !== "Needs Rebuild" && (
            <div className="relative h-2 bg-gray-100 rounded-full overflow-hidden">
              <div
                className="absolute left-0 top-0 h-full transition-all duration-500 ease-out "
                style={{
                  width: `${Math.min(adjustedPercentage, 100)}%`,
                  backgroundColor:
                    progressColor === "bg-green-500"
                      ? "#22c55e"
                      : progressColor === "bg-yellow-500"
                        ? "#eab308"
                        : progressColor === "bg-gray-300"
                          ? "#d1d5db"
                          : "#3b82f6",
                }}
              />
            </div>
          )}
        </div>

        {/* Show processing status when dropdown is open */}
        {isDropdownOpen && (
          <ProcessingStatus
            buildId={buildId}
            processingStatus={buildProgress[buildId]}
            onClose={onToggleDropdown}
          />
        )}
      </>
    );
  };

  useEffect(() => {
    if (currentStep == 3) {
      setShowExtractionModal(false);
    }
  }, [currentStep])



  const isButtonDisabled = (selectedBranch) => {
    return (
      selectedBranch?.kg_creation_status === 1 && !buttonEnable
    );
  };


  const handleInitializeExtraction = async () => {
    setExtractLoad(true)

    let user_level = 1;

    try {
      const data = await configureNodeWithAgent({
        node_id: projectId,
        node_type: "project",
        user_level: user_level,
        project_id: projectId,
        configurations: {
          "configure": true,
          "repo": selectedRepositories,
          "projectId": projectId,
          "work_item": {
            "configure": true
          },
          "requirements": {
            "configure": true,
            "epic": {
              "configure": true,
              "user_story": {
                "configure": true,
                "task": {
                  "configure": true
                }
              }
            }
          },
          "architecture": {
            "configure": true,
            "architectural_requirements": {
              "configure": true
            },
            "system_context": {
              "configure": true,
              "container": {
                "configure": true,
                "components": {
                  "configure": true,
                  "design": {
                    "configure": true
                  },
                  "interface": {
                    "configure": true
                  }
                }
              }
            }
          }
        }

      });

      if (data.error === "You already have a task in progress. Please cancel the current task to start a new one.") {

        if (currentTaskId !== extractTaskId) {
          showAlert("You already have an auto-configure task in progress. Please cancel the current task to start a new one.", "warning");
        } else {
          showAlert("An extraction task is already in progress. Please wait for it to complete or cancel it before starting a new one.", "warning");
        }
        // setConfiglabel("auto-extract");
      }
      else {
        showAlert("Codebase extraction initialized successfully! ", "success")
        setCurrentTaskId(data.task_id);
        setExtract(true)
        setConfiglabel("auto-extract")
        setExtractTaskId(data.task_id)
        sessionStorage.setItem(`${projectId}-auto-extract`, "true")
        setTaskStatus(true)
        setExtractLoad(false)
        handleCloseAsset()
      }
    } catch (error) {
      
    } finally {
      setExtractLoad(false)
    }
  };



  return (
    <>
      <div className="flex flex-col h-[calc(100vh-80px)] ">
        <WebSocketSessionsComponent repositories={kgResponse.details} />
        {!isRepoModalOpen && (
          <div className="px-4 py-4 flex items-center justify-between gap-4 bg-white">
            <div className="relative flex-1">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search repository"
                className="w-full pl-12 pr-8 py-1 border rounded-lg focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                value={searchTerm}
                onChange={(e) => {
                  setCurrentPage(1);
                  setSearchTerm(e.target.value);
                }}
              />
            </div>

            <DynamicButton
              variant="primary"
              icon={RefreshCw}
              onClick={() => handleCallApi()}
              tooltip="Refresh"
            />

            <DynamicButton
              variant="primary"
              icon={Plus}
              text="Add Repository"
              onClick={handleAddRepoClick}
            />

            <DynamicButton
              variant="secondary"
              icon={FileText}
              text="Update Overall Manifest"
              onClick={() => setShowManifestModal('project')}
              tooltip="Update project manifest"
            />

            <div className="flex items-center space-x-2">
              {taskStatusUpdate?.toLowerCase() === "in_progress" && extract && (
                <div className="w-2 h-2 bg-green-500 rounded-full animate-ping"></div>
              )}
              <DynamicButton
                variant="primaryLegacy"
                tooltip="Click to start extracting the codebase."
                icon={Code}
                text={extractLoad ? "Processing..." : taskStatus ? "Extracting.." : taskStatusUpdate?.toLowerCase() === "in_progress" && extract ? "Extracting" : "Extract"}
                onClick={() => handleInitializeExtraction()}
                disable={
                  extractLoad ||
                  selectedRepositories.length === 0 ||
                  !selectedRepositories.every((repo) => repo.status === "Completed" || repo.status==="Rebuilding")
                }
              />
            </div>
          </div>)}

        {/* Main content area with repositories */}
        {!isRepoModalOpen && (
          <div className="flex-1 flex flex-col min-h-0">
            <div className="flex-1 overflow-y-auto px-4 max-h-[50vh]">
              <div className=" mx-auto">
                {/* Repository list content - keeping existing structure */}
                {isLoading ? (
                  <div className="space-y-4 py-4">
                    {/* Loading components */}
                  </div>
                ) : repositories.length === 0 ? (
                  <EmptyStateView type="RepoNotFound" onClick={handleAddRepoClick} />
                ) : (
                  <div className="space-y-4 py-4">
                    {getCurrentPageItems().map((repo, index) => {
                      // Repository item rendering - keeping existing structure
                      const selectedBranch = repo.service == "github" ?
                        repo.branches.find(
                          (b) => b.name === repo.selectedBranch
                        ) : "";

                      const buildId = getBuildID(repo.id, repo.service);
                      const hasUpstream = repo.service == "localFiles"? false : selectedBranch?.upstream === true || selectedBranch?.kg_creation_status == 3;

                      return (
                        <div key={repo.id} className="bg-white rounded-lg border border-gray-200 p-4 w-full">
                          {/* Repository content - keeping existing structure */}
                          <div className="flex justify-between items-start">
                            <div className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                checked={checkRepoSelected(repo.id)}
                                onChange={() => toggleRepoSelect(repo)}
                                className="w-5 h-5 self-start mr-3 rounded-md border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <div className="text-blue-600 p-1 rounded-lg bg-blue-50 flex items-center justify-center w-8 h-8 -translate-y-4">
                                <Code className="w-5 h-5 text-blue-600" />
                              </div>
                              <div>
                                <h3 className="font-weight-medium text-gray-900">
                                  {repo.name}
                                </h3>
                                <div className="flex items-center space-x-2 typography-body-sm text-gray-500 mt-2">
                                  <span className="py-1 px-2 bg-gray-100 rounded-lg ">
                                    {repo.repoType}
                                  </span>
                                  {repo.service !== "localFiles" && (
                                    <div className="flex items-center space-x-2">
                                      <GitBranch className="w-5 h-5 text-gray-500" />
                                      <select
                                        className="border border-gray-300 rounded-lg py-1 px-2 pr-10 bg-gray-50 typography-body-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        value={repo.selectedBranch}
                                        onChange={(e) => handleBranchChange(e, repo.id)}
                                      >
                                        {repo.branches.map((branch) => (
                                          <option key={branch.name} value={branch.name}>
                                            {branch.name}
                                          </option>
                                        ))}
                                      </select>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center space-x-4">
                              <div className="typography-body-sm text-gray-500">
                                {selectedBranch?.lastUpdated && (
                                  <>
                                    <span className="font-weight-semibold">Modified</span>{" "}
                                    {formatListDate(selectedBranch.lastUpdated)}
                                  </>
                                )}


                              </div>
                              
                              {hasUpstream && (
                                <button
                                  onClick={() => handleSync(projectId, repo.id, repo.selectedBranch, buildId, repo.status)}
                                  title="Sync repo"
                                  disabled={isButtonDisabled(selectedBranch)}
                                  className={`${isButtonDisabled(selectedBranch)
                                      ? "cursor-not-allowed text-gray-300"
                                      : "text-blue-600 hover:text-blue-800 relative"
                                    }`}
                                >
                                  <RefreshCw size={20} className="animate-spin-slow" />
                                  <span className="absolute -top-1 right-0 h-2 w-2 bg-red-500 rounded-full animate-ping"></span>
                                </button>
                              )}

                            {/* Add view manifest button */}
                              <button
                                  onClick={() => {
                                    setSelectedManifestData({
                                      repositoryId: repo.id,
                                      branch: repo.selectedBranch
                                    })
                                    setShowManifestModal('branch');
                                  }}
                                  title="View / Update Manifest"
                                  className="flex items-center gap-1"
                                >
                                  <FileText className="w-5 h-5 text-gray-400 hover:text-gray-600 cursor-pointer" />
                                </button>

                              <button onClick={() => setDeleteRepoModalId(repo.id)}>
                                <Trash2 className="w-5 h-5 text-red-500 hover:text-gray-500" />
                              </button>

                              {deleteRepoModalId == repo.id && (
                                <DeleteRepoModal
                                  onDelete={async () => {
                                    await handleRemoveRepository(buildId, repo.service);
                                  }}
                                  onClose={() => setDeleteRepoModalId(null)}
                                />
                              )}
                            </div>
                          </div>

                          {/* Progress bar component - keeping existing structure */}
                          <DynamicProgressBar
                            buildId={buildId}
                            status={repo.status}
                            isDropdownOpen={openDropdowns[buildId] || false}
                            onToggleDropdown={() => toggleDropdown(buildId)}
                          />

                          {showExtractionModal && (
                            <ExtractionProgressModal
                              handleModalClose={() => setShowExtractionModal(false)}
                              currentStep={currentStep}
                              progress={progress}
                            />
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>

            {/* Pagination */}
            {repositories.length > 0 && (!searchTerm || filteredRepositories.length > 0) && (
              <div className="bg-white mt-auto w-full ">
                <div className="px-4 py-3">
                  <Pagination
                    currentPage={currentPage}
                    pageCount={Math.ceil(
                      (searchTerm
                        ? filteredRepositories.length
                        : repositories.length) / pageSize
                    )}
                    pageSize={pageSize}
                    totalItems={
                      searchTerm ? filteredRepositories.length : repositories.length
                    }
                    onPageChange={setCurrentPage}
                    onPageSizeChange={setPageSize}
                    pageSizeOptions={[5, 10, 15, 20]}
                  />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Repository Addition Modal */}
        {isRepoModalOpen && (
          <RepositoryAddition
            scmConfigurations={scmConfigurations}
            onSCMTypeSelect={handleSCMTypeSelect}
            addBtnClicked={addBtnClicked}
            onClose={handleAddRepoClose}
            setShowRepos={setShowRepos}
            showRepos={showRepos}
            onImport={handleCallApi}
            setRepositories={setRepositories}
            setActiveTab={setActiveTab}
            repositories={repositories}
            updateBuildStatuses={updateBuildStatuses}
            onRefresh={handleCallApi}
          />
        )}

        {/* Project-level Manifest Update Modal */}
        {showManifestModal === 'project' && (
          <ManifestUpdateModal
            projectId={projectId}
            onClose={() => setShowManifestModal(null)}
              onSuccess={() => {
              showAlert("Project manifest updated successfully!", "success");
            }}
            title="Update Project Manifest"
            description="This manifest will be stored for the entire project and can include container definitions, build configurations, and project metadata."
          />
        )}

        {/* Branch-level Manifest Update Modal */}
        {showManifestModal === 'branch' && (
          <ManifestUpdateModal
            projectId={projectId}
            onClose={() => setShowManifestModal(null)}
            onSuccess={() => {
              showAlert("Branch manifest updated successfully!", "success");
            }}
            selectedRepository={selectedManifestData?.repositoryId}
            selectedBranch={selectedManifestData?.branch}
            title="Update Branch Manifest"
            description="This manifest will be stored for the selected branch and can include container definitions, build configurations, and project metadata."
          />
        )}
      </div>

    </>
  );
};

export default RepositoryList;
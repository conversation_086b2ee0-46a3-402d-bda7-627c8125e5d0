import React, { useState, useEffect, useCallback } from "react";
import { X, List, Search } from "lucide-react";

const NavigationTree = ({ treeData, handleScrollToSection }) => {
  const [activeSection, setActiveSection] = useState(null);
  const [isMinimized, setIsMinimized] = useState(true);
  const [navWidth, setNavWidth] = useState('3rem');
  const [searchTerm, setSearchTerm] = useState('');

  const calculateNavWidth = useCallback(() => {
    if (isMinimized) return '3rem';
    const screenWidth = window.innerWidth;
    if (screenWidth >= 1600) return '12vw';
    if (screenWidth >= 1280) return '15vw';
    if (screenWidth >= 1024) return '18vw';
    return '12vw'; // default fallback
  }, [isMinimized]);

  useEffect(() => {
    const handleResize = () => setNavWidth(calculateNavWidth());
    setNavWidth(calculateNavWidth());
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [calculateNavWidth]);

  useEffect(() => {
    const handleScroll = () => {
      const sectionIds = treeData.map((node) => node.id);
      let currentSection = "";
      for (const sectionId of [...sectionIds].reverse()) {
        const element = document.getElementById(sectionId);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 300) {
            currentSection = sectionId;
            break;
          }
        }
      }
      if (currentSection && currentSection !== activeSection) {
        setActiveSection(currentSection);
      }
    };

    const mainContent = document.getElementById("main-content");
    if (mainContent) {
      const debouncedHandler = () => {
        clearTimeout(timeout);
        timeout = setTimeout(() => handleScroll(), 100);
      };
      let timeout;
      mainContent.addEventListener("scroll", debouncedHandler);
      handleScroll();

      return () => {
        mainContent.removeEventListener("scroll", debouncedHandler);
        clearTimeout(timeout);
      };
    }
  }, [treeData, activeSection]);

  const filteredData = treeData.filter(node =>
    node.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const toggleNavigation = () => setIsMinimized(!isMinimized);

  const scrollToSection = (sectionId) => {
    setActiveSection(sectionId);
    handleScrollToSection(sectionId);
  };

  return (
    <div
      style={{ width: navWidth }}
      className="relative overflow-hidden transition-all duration-300 ease-in-out h-full p-2"
    >
      {/* Expand Button */}
      {isMinimized && (
        <div className="absolute top-0 left-0 w-full flex justify-center">
          <button
            onClick={toggleNavigation}
            className="px-2 py-1 flex items-center gap-2 rounded shadow hover: transition"
            aria-label="Expand Navigation"
          >
            <List size={20} />
            <span className="typography-body-sm font-weight-medium">{treeData.length}</span>
          </button>
        </div>
      )}

      {/* Expanded View */}
      <div className={`${isMinimized ? 'hidden' : 'block'} transition-all duration-300`}>
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-3 border-b">
          <h3 className="typography-body-sm font-weight-medium">Contents</h3>
          <button
            onClick={toggleNavigation}
            className="p-1 rounded-full hover transition"
            aria-label="Minimize Navigation"
          >
            <X size={20} />
          </button>
        </div>

        {/* Search */}
        <div className="px-3 py-2">
          <div className="relative">
            <Search size={16} className="absolute left-2 top-2 text-gray-400" />
            <input
              type="text"
              placeholder="Search contents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-8 pr-2 py-1 typography-body-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Content List */}
        <div className="overflow-y-auto" style={{ maxHeight: 'calc(100vh - 220px)' }}>
          <nav>
            {filteredData.length > 0 ? (
              <ul className="list-none p-0 m-0">
                {filteredData.map((node) => (
                  <li key={node.id}>
                    <a
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        scrollToSection(node.id);
                      }}
                      className={`block px-4 py-2 typography-body-sm ${
                        activeSection === node.id
                          ? 'text-blue-600 font-weight-semibold underline'
                          : 'text-gray-700'
                      } hover:underline`}
                    >
                      {node.name}
                    </a>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="text-center py-10 text-gray-500">
                {searchTerm ? 'No results found' : 'No contents available'}
              </div>
            )}
          </nav>
        </div>
      </div>
    </div>
  );
};

export default NavigationTree;

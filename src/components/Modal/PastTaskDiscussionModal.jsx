import React, { useState, useEffect } from 'react';
import TableComponent from "../SimpleTable/pastDiscussionTable"
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { FaTimes } from 'react-icons/fa';
import { formatUTCToLocal } from "@/utils/datetime";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { updateSessionStorageBackHistory } from '@/utils/helpers';

const Spinner = () => (
  <div className="flex justify-center items-center h-64">
    <div className="relative">
      <div className="w-12 h-12 rounded-full border-4 border-gray-200"></div>
      <div className="w-12 h-12 rounded-full border-4 border-t-blue-500 animate-spin absolute top-0 left-0"></div>
    </div>
  </div>
);

const PastTaskDiscussionModal = ({ isOpen, onClose, tasks, totalCount, skip, onPageChange, onLimitChange, title = '' }) => {
  const [loading, setLoading] = useState(true); // Initialize loading state to true
  const searchParams = useSearchParams();
  const [taskList, setTaskList] = useState([]);

  const [count, setCount] = useState(0)
  const router = useRouter();
  const pathname = usePathname();
  const nodeId = searchParams.get("node_id");
  const discussionType = searchParams.get("discussion_type");
  const fullUrl = `${pathname}?${searchParams.toString()}`;



  // Fetching tasks when the modal is opened
  useEffect(() => {
    if (isOpen) {
      setLoading(true); // Set loading to true when modal opens
      setTimeout(() => {
        setLoading(false); // Simulate fetching data and stop loading after 2 seconds (or your data fetch)
      }, 2000);
    }
  }, [isOpen]);

  // Watch for changes in the tasks prop to set loading to false
  useEffect(() => {
    if (tasks.length > 0) {
      setLoading(false); // Set loading to false once tasks are loaded
      setTaskList(tasks)
      setCount(totalCount)
    }
  }, [tasks]);

  if (!isOpen) return null;

  const headers = [
    { key: 'id', label: 'Discussion ID' },
    { key: 'title', label: 'Title' },
    { key: 'createdAt', label: 'Created At' },
    { key: 'createdBy', label: 'Created By' },
    { key: 'discussionType', label: 'Discussion Type' }
  ];

  const sortableColumns = {
    id: true,
    title: true,
    createdAt: true,
  };

  const formattedTasks = taskList.map(task => ({
    id: task.id,
    title: task.title,
    createdAt: formatUTCToLocal(task.created_at),
    createdBy: task.created_by?.name,
    status: task.status,
    discussionType: task.discussion_type
  }));

  const handleRowClick = (id, type = "existing") => {
    const newSearchParams = new URLSearchParams();
    newSearchParams.set("node_id", nodeId);
    newSearchParams.set("discussion_id", id);
    newSearchParams.set("discussion", type);
    newSearchParams.set('view_past_discussions', 'true')
    updateSessionStorageBackHistory(1);
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };


  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40">
      <div className="bg-white rounded-lg shadow-lg w-[90%] h-[90%] flex flex-col">
        <div className="p-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
          <h4 className="user-panel-sub-head">{title}</h4>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center border border-gray-300 bg-white text-gray-500 hover:text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            <FaTimes size={16} />
          </button>
        </div>
        <div className="flex-grow overflow-auto custom-scrollbar p-5">
          {loading ? (
            <Spinner />
          ) : (
            formattedTasks.length > 0 ? (
              <TableComponent
                // title="Past Tasks"
                totalCount={totalCount}
                data={formattedTasks}
                headers={headers}
                sortableColumns={sortableColumns}
                onRowClick={handleRowClick}
                component = "code-generation"
                onPageChange={onPageChange}
                onPageSizeChange={onLimitChange}
              />
            ) : (
              <div className="text-center flex justify-center  items-center"><EmptyStateView type="discussions" onClick={() => {}} /></div> 
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default PastTaskDiscussionModal;

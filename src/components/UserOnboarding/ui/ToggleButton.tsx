// First define the BillingCycle type
type BillingCycle = 'monthly' | 'yearly';

// Then your component
export const ToggleButton: React.FC<{
  billingCycle: BillingCycle;
  setBillingCycle: (cycle: BillingCycle) => void;
}> = ({ billingCycle, setBillingCycle }) => {
  return (
    <div className="flex items-center gap-3">
      <span className={`typography-body-sm ${billingCycle === 'monthly' ? 'text-gray-900 font-weight-medium' : ''}`}>
        Monthly
      </span>

      <button
        onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors 
          ${billingCycle === 'yearly' ? 'bg-blue-600' : 'bg-gray-200'}`}
      >
        <span className="sr-only">Toggle billing cycle</span>
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform
            ${billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'}`}
        />
      </button>

      <span className={`typography-body-sm ${billingCycle === 'yearly' ? 'text-gray-900 font-weight-medium' : ''}`}>
        Yearly
      </span>
    </div>
  );
};
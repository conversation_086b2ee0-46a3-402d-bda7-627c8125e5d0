"use client";

import React, { useState, useEffect, useContext, useRef } from "react";
import { Gith<PERSON>, <PERSON>, CheckCircle2, XCircle, RefreshCw, Code, Trash2, Upload, FileUp } from "lucide-react";
import {
  checkGitConnectionStatus,
  gitHubConnect,
  gitHubDisconnect,
} from "@/utils/gitAPI";
import ConnectedRepositorySection from "./ConnectedRepositorySection";
import { AlertContext } from "../NotificationAlertService/AlertList";
import { clone_and_build, getkginfo, fetchRepositoryDetails, getRepoDetails, getRepoBranches, getBranchDetails } from "@/utils/gitAPI";
import { useWebSocket } from "../Context/WebsocketContext";
import { FiArrowRight } from "react-icons/fi";
import { ProjectAssetContext } from "@/components/Context/ProjectAssetContext";
import { usePathname } from "next/navigation";
import { getScmConfiguration } from '@/utils/api';
import { ChevronDown } from "lucide-react";
import { DynamicButton } from "../UIComponents/Buttons/DynamicButton";
import { ingestLocalFiles } from "@/utils/api";
import { BootstrapTooltip } from "../UIComponents/ToolTip/Tooltip-material-ui";
import { useUser } from "../Context/UserContext";

const FileInfo = ({ file, onRemove }) => {
  const determineFileType = (file) => {
    const type = file.type || "";
    const name = file.name?.toLowerCase() || "";

    if (type.startsWith("image/")) {
      return "image";
    } else if (
      name.endsWith(".pdf") ||
      name.endsWith(".doc") ||
      name.endsWith(".docx") ||
      name.endsWith(".txt") ||
      name.endsWith(".xls") ||
      name.endsWith(".xlsx") ||
      name.endsWith(".ppt") ||
      name.endsWith(".pptx")
    ) {
      return "document";
    } else if (
      name.endsWith(".zip") ||
      name.endsWith(".rar") ||
      name.endsWith(".7z") ||
      name.endsWith(".tar") ||
      name.endsWith(".gz")
    ) {
      return "zip";
    } else {
      return "default";
    }
  }

  const fileType = determineFileType(file);

  const getTruncatedName = (fileName) => {
    if (!fileName || typeof fileName !== "string") return "";

    const dotIndex = fileName.lastIndexOf(".");
    if (dotIndex === -1 || dotIndex === 0) return fileName;

    const name = fileName.substring(0, dotIndex);
    const ext = fileName.substring(dotIndex);

    if (name.length <= 5) return name + ext;

    return name.substring(0, 5) + "..." + ext;
  };

  const getRelativeSize = (size) => {
    if (typeof size !== "number" || size < 0) return "0 B";

    const units = ["B", "KB", "MB", "GB", "TB"];
    let i = 0;

    while (size >= 1024 && i < units.length - 1) {
      size /= 1024;
      i++;
    }

    const isWhole = size % 1 === 0;
    return `${isWhole ? size : Math.round(size * 100) / 100} ${units[i]}`;
  };

  return (
    <BootstrapTooltip title={file.name} placement="bottom">
      <div className="rounded-md bg-orange-50 w-[120px] flex flex-col shrink-0 gap-1 items-center py-2 relative">
        <button
          className="absolute w-8 h-8 rounded-full bg-white -top-1 -right-1 flex items-center justify-center border border-orange-400 hover:bg-orange-100"
          onClick={() => onRemove(file)}
        >
          <Trash2 size={16} color="orange" />
        </button>

        <img src={`/images/file-types/file-type-${fileType}.png`} className="h-[80px]" />
        <div>
          <p className="mt-2">{getTruncatedName(file.name)}</p>
          <p>{getRelativeSize(file.size)}</p>
        </div>
      </div>
    </BootstrapTooltip>
  )
}

function PersonalRepositorySection({
  onSCMTypeSelect,
  onClose,
  onImport,
  setShowRepositories,
  showRepositories,
  addBtnClicked,
  repositories,
  setRepositories,
  setActiveTab,
  showDetails,
  handleCloseModel,
  handleRefresh,
  updateBuildStatuses
}) {
  const [connectionStatus, setConnectionStatus] = useState({
    isConnected: false,
    loading: true,
    error: null,
    username: null,
  });

  const { connectToSession, disconnectFromSession } = useWebSocket();
  // const [showRepositories, setShowRepositories] = useState(false);

  const [selectedBranch, setSelectedBranch] = useState("");
  const [activeTabVal, setActiveTabVal] = useState('personal');
  const [branches, setBranches] = useState([]);
  const [showBranchDropdown, setShowBranchDropdown] = useState(false);
  const [isFetchingBranches, setIsFetchingBranches] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const [gitRepoId, setGitRepoId] = useState("");
  const [gitUrl, setGitUrl] = useState("");
  const [isCloning, setIsCloning] = useState(false);
  const { updateSessionId, repositoryDetails, setRepositoryDetails, setScanningRepoId, scanningRepoId } = useContext(ProjectAssetContext);
  const pathname = usePathname();
  const projectId = pathname.split("/")[3];
  const [scmConfigurations, setSCMConfigurations] = useState([]);
  const [error, setError] = useState(null);
  const [selectedSCMId, setSelectedSCMId] = useState("");
  const [searchedBranch, setSearchedBranch] = useState("");
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const fileInputRef = useRef(null);
  const dropZoneRef = useRef(null);
  const dragCounter = useRef(0);
  const [dropzoneHeight, setDropzoneHeight] = useState("200px");
  const [showIngestModal, setShowIngestModal] = useState(false);
  const [localProjectName, setLocalProjectName] = useState("");
  const { is_free_user } = useUser();
  const [isSearchingBranch, setIsSearchingBranch] = useState(false);

  useEffect(() => {
    checkConnection();
  }, []);

  const handleSelect = () => {
    if (gitUrl && branches.length === 0) {
      const repoInfo = extractRepoInfo(gitUrl);
      if (repoInfo) {
        fetchBranches(repoInfo.owner, repoInfo.repo);
      } else {
        showAlert("Invalid repository URL format", "error");
      }
    }
    setShowBranchDropdown((prev) => !prev);
  }

  const handleFileInputClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }

  const handleIngestFiles = async () => {
    if (uploadedFiles.length == 0) {
      setLocalProjectName("");
      setShowIngestModal(false);
      return;
    };

    try {
      showAlert("Uploading Files", "info");
      setShowIngestModal(false);
      setUploadedFiles([]);
      setLocalProjectName("");
      const response = await ingestLocalFiles(projectId, localProjectName, uploadedFiles);
      if (response.status == "success") {
        if (response?.buildIds) {
          updateBuildStatuses(response.buildIds, "ingest");
        }
      }
      else {
        showAlert("An error occured", "error");
      }

      onClose()
      handleRefresh && handleRefresh();
    }
    catch (error) {
      showAlert("An error occured", "error");
    }
  }

  const checkBranchExistence = async (branchName, owner, repo) => {
    try {
      setIsSearchingBranch(true);
      const response = await getBranchDetails(branchName, owner, repo);
      
      if (response && Object.keys(response).length > 0) {
        return true;
      } else {
        return false;
      }
    }
    catch (error) {
      return false;
    }
    finally{
      setIsSearchingBranch(false);
    }
  }

  const handleSelectBranch = (branchName) => {
    setSelectedBranch(branchName);
    setShowBranchDropdown(false);
    setSearchedBranch('');
  }

  const checkBranchDetails = async (e) => {
    e.preventDefault();

    const repoInfo = extractRepoInfo(gitUrl);

    if (repoInfo) {
      const branchName = searchedBranch;
      const branchExists = await checkBranchExistence(branchName, repoInfo.owner, repoInfo.repo);
      if (branchExists) {
        handleSelectBranch(branchName);
      }
      else {
        showAlert("Couldn't find branch. Make sure the name is correct.", "error");
      }
    }
  }

  const handleFileUpload = (e) => {
    const selectedFiles = Array.from(e.target.files);
    const filteredFiles = selectedFiles.filter((file) => file.size < 50 * 1024 * 1024); // 50MB in bytes
    if (filteredFiles.length !== selectedFiles.length) {
      if (selectedFiles.length == 1) {
        showAlert("File size greater than 50MB! Cannot upload.", "error");
      }
      else if (filteredFiles.length > 0) {
        showAlert("Files greater than 50MB have been skipped.", "warning");
      }
      else {
        showAlert("All files are greater than 50 MB! Cannot upload.", "error");
      }
    }
    setUploadedFiles((prev) => [...prev, ...filteredFiles]);
    e.target.value = null;
  };

  const handleDrop = (e) => {
    e.preventDefault();
    dropZoneRef.current.classList.remove("bg-orange-100");
    dropZoneRef.current.classList.add("bg-orange-50");
    dragCounter.current = 0;
    const selectedFiles = Array.from(e.dataTransfer.files);
    const filteredFiles = selectedFiles.filter((file) => file.size < 50 * 1024 * 1024 && file.size > 0); // 50MB in bytes
    const heavierFiles = selectedFiles.filter((file) => file.size >= 50 * 1024 * 1024)
    const folders = selectedFiles.filter((file) => file.size <= 0); //skipping folders as they have 0 mb
    if (filteredFiles.length !== selectedFiles.length) {
      if (heavierFiles.length == 1) {
        showAlert("File size greater than 50MB! Cannot upload.", "error");
      }
      else if (folders.length == 1) {
        showAlert("Cannot upload a folder! Please compress and reupload", "error");
      }
      else if (filteredFiles.length > 0) {
        showAlert("Files greater than 50MB and folders have been skipped.", "warning");
      }
    }

    setUploadedFiles((prev) => [...prev, ...filteredFiles]);
  }

  const handleDragEnter = (e) => {
    handleDrag(e);
    dragCounter.current++;
    if (dragCounter.current === 1) {
      dropZoneRef.current.classList.remove("bg-orange-50");
      dropZoneRef.current.classList.add("bg-orange-100");
    }
  }

  const handleDragLeave = (e) => {
    handleDrag(e);
    dragCounter.current--;
    if (dragCounter.current === 0) {
      dropZoneRef.current.classList.remove("bg-orange-100");
      dropZoneRef.current.classList.add("bg-orange-50");
    }
  }

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
  }

  const handleRemove = (file) => {
    setUploadedFiles(uploadedFiles.filter((fileItem) => fileItem !== file))
  }

  const checkConnection = async () => {
    try {
      setConnectionStatus((prev) => ({ ...prev, loading: true }));
      const response = await checkGitConnectionStatus();
      setConnectionStatus({
        isConnected: response.git_connected,
        loading: false,
        error: null,
        username: response.username,
      });
    } catch (error) {
      setConnectionStatus({
        isConnected: false,
        loading: false,
        error: "Failed to check connection status",
        username: null,
      });
    }
  };

  const extractRepoInfo = (url) => {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split("/");
      // Remove empty strings and .git extension
      const cleanParts = pathParts
        .filter((part) => part)
        .map((part) => part.replace(".git", ""));

      if (cleanParts.length >= 2) {
        return {
          owner: cleanParts[0],
          repo: cleanParts[1],
        };
      }
      return null;
    } catch (error) {
      return null;
    }
  };

  useEffect(() => {
    fetchSCMConfigurations();
  }, []);

  const fetchSCMConfigurations = async () => {

    try {

      const response = await getScmConfiguration();
      if (response.status === "success") {
        setSCMConfigurations(response.data?.configurations || []);
      }
    } catch (err) {
      
      setError('Failed to fetch SCM configurations');
    } finally {

    }
  };

  const fetchBranches = async (owner, repo) => {
    try {
      setIsFetchingBranches(true);

      // First fetch repository details to get the repo_id
      const repoData = await getRepoDetails(owner, repo);
      if (!repoData) {
        throw new Error("Failed to fetch repository details");
      }

      const branchResponse = await getRepoBranches(owner, repo);

      if (branchResponse == []) {
        throw new Error("Failed to fetch branches");
      }

      const branchList = branchResponse.map((branch) => branch.name);
      setBranches(branchList);


      // Store repo_id for later use
      setGitRepoId(repoData.id.toString());


      // If branches are found, set the first one as selected
      if (branchList.length > 0) {
        setSelectedBranch(branchList[0]);
      }
    } catch (error) {
      
      showAlert("Error fetching repository data", "error");
      setBranches([]);
    } finally {
      setIsFetchingBranches(false);
    }
  };

  const handleRepositoriesListClick = () => {
    setShowRepositories(true);
  };

  const handleClose = () => {
    setShowRepositories(false);
    setGitUrl("");
    if (addBtnClicked) {
      onClose();
    }
  };

  const handleConnect = async () => {
    try {
      const response = await gitHubConnect();
      if (response.url) {
        const newWindow = window.open(response.url, "_blank");
        setTimeout(() => {
          checkConnection();
        }, 3000);

        const checkWindowClosed = setInterval(() => {
          if (newWindow.closed) {
            clearInterval(checkWindowClosed);
            checkConnection();
          }
        }
          , 500);
      } else {
        await checkConnection();
      }
    } catch (error) {
      setConnectionStatus((prev) => ({
        ...prev,
        error: "Failed to initiate GitHub connection",
      }));
    }
  };

  const handleDisconnect = async () => {
    try {
      await gitHubDisconnect();
      setShowRepositories(false);
      await checkConnection();
    } catch (error) {
      setConnectionStatus((prev) => ({
        ...prev,
        error: "Failed to disconnect from GitHub",
      }));
    }
  };

  const validateGitUrl = (url) => {
    const repoInfo = extractRepoInfo(url);
    return repoInfo !== null;
  };

  const handleGitUrlChange = (e) => {
    setGitUrl(e.target.value);
    setBranches([]); // Clear branches when URL changes
    setGitRepoId(""); // Clear repo_id when URL changes
    setSelectedBranch(""); // Clear selected branch when URL changes
  };

  const handleImportGitUrl = async () => {
    if (!gitUrl) {
      showAlert("Please enter a Git Public URL.", "warning");
      return;
    }

    if (!validateGitUrl(gitUrl)) {
      showAlert(
        "Invalid Git Public URL format. Please enter a valid GitHub repository URL.",
        "error"
      );
      return;
    }

    if (!selectedBranch) {
      showAlert("Please select a branch.", "warning");
      return;
    }

    setIsCloning(true);

    // Extract repository name from the Git Public URL
    const repoUrl = gitUrl.replace(/\.git$/, "");
    const repoName = repoUrl.split("/").pop();
    const owner_name = repoUrl.split("/")[3];

    // Prepare the payload for the POST request
    const payload = {
      project_id: parseInt(projectId, 10),
      repositories: [
        {
          repo_id: gitRepoId, // Use the stored repo_id
          repo_name: owner_name + "/" + repoName,
          branch_name: selectedBranch,
          repo_type: "public",
          associated: true,
        },
      ],
    };

    try {
      const response = await clone_and_build(payload);

      if (response?.buildIds) {
        updateBuildStatuses(response.buildIds, "clone")
      }
      const kgResponse = await getkginfo(projectId, true);

      showAlert("Git Public URL imported successfully!", "success");
      setGitUrl("");
      onImport();

      handleRefresh && handleRefresh();

    } catch (error) {
      
      showAlert("Error importing Git Public URL. Please try again.", "error");
    } finally {
      setIsCloning(false);
      onClose();
    }
  };

  function processRepositories(response, selectedRepositories) {
    // Map the repository details
    const listRepos = response.details.map((repo) => {
      const firstBranch = repo.branches[0];
      const firstBranchStatus = firstBranch?.builds?.kg_creation_status;


      return {
        id: repo.git_url.split("/").pop().split(".")[0],
        name: repo.git_url.split("/").pop().split(".")[0],
        gitUrl: repo.git_url.replace(".git", ""),
        repoType: repo.repo_type,
        branches: repo.branches.map((branch) => ({
          name: branch.name,
          buildId: branch.builds?.build_id,
          kg_creation_status: branch.builds?.kg_creation_status,
          status:
            branch.builds?.kg_creation_status === 0
              ? "Not Started"
              : branch.builds?.kg_creation_status === 1
                ? "In Progress"
                : branch.builds?.kg_creation_status === 2
                  ? "Completed"
                  : "Failed",
          lastUpdated: branch.builds?.last_updated,
          type:
            branch.name === "main" || branch.name === "master"
              ? "main"
              : branch.name.startsWith("feature/")
                ? "feature"
                : branch.name.startsWith("fix/")
                  ? "fix"
                  : branch.name.startsWith("dev")
                    ? "development"
                    : "other",
        })),
        selectedBranch: firstBranch?.name || "main",
        status:
          firstBranchStatus === 0
            ? "Not Started"
            : firstBranchStatus === 1
              ? "In Progress"
              : firstBranchStatus === 2
                ? "Completed"
                : "Failed",
        created_at: response.created_at,
      };
    });

    // Extract build IDs for selected repositories
    const buildIds = selectedRepositories.map((repoId) => {
      const repo = listRepos.find((r) => r.id === repoId.name);

      if (!repo) {
        throw new Error(`Repository with ID ${repoId.name} not found.`);
      }


      if (!repo.selectedBranch) {
        throw new Error(`No branch selected for repository ${repo.name}.`);
      }


      const selectedBranchObj = repo.branches.find(
        (b) => b.name === repo.selectedBranch
      );


      if (!selectedBranchObj) {
        throw new Error(
          `Selected branch '${repo.selectedBranch}' not found for repository ${repo.name}.`
        );
      }


      return selectedBranchObj.buildId;
    });

    return { buildIds };

    return { buildIds };
  }

  const LoadingOption = () => (
    <option className="text-gray-500" disabled>
      Loading branches...
    </option>
  );


  const handleScanRepo = async (scm_id) => {
    setScanningRepoId(scm_id)
    setSelectedSCMId(scm_id)
    try {
      const response = await fetchRepositoryDetails(scm_id)
      setRepositoryDetails(response)
      setShowRepositories(true);
      setScanningRepoId(null)

    } catch (error) {
      setScanningRepoId(null)
      setSelectedSCMId(null)
    } finally {
      setScanningRepoId(null)
    }

  }

  if (showRepositories) {
    return (
      <div className="h-full flex flex-col overflow-hidden">
        <div className="h-full">
          <ConnectedRepositorySection
            onClose={handleClose}
            onImport={onImport}
            repositoriesVal={repositories}
            setRepositories={setRepositories}
            activeTabVal={activeTabVal}
            encryptedSCMId={selectedSCMId}
            organizationName={selectedSCMId ? scmConfigurations.find(
              config => config.encrypted_scm_id === selectedSCMId
            )?.credentials.organization : null}
            updateBuildStatuses={updateBuildStatuses}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col overflow-hidden min-h-[550px]">
      <div className="">
        <nav className="flex justify-center space-x-8 border-b border-gray-200 -mt-4">
          <button
            onClick={() => setActiveTabVal('personal')}
            className={`py-4 px-1 border-b-2 font-weight-medium typography-body-sm ${activeTabVal === 'personal'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
          >
            Personal Accounts
          </button>
          <button
            onClick={() => setActiveTabVal('organization')}
            className={`py-4 px-1 border-b-2 font-weight-medium typography-body-sm ${activeTabVal === 'organization'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
          >
            Organization Accounts
          </button>
          <button
            onClick={() => setActiveTabVal('localFiles')}
            className={`py-4 px-1 border-b-2 font-weight-medium typography-body-sm ${activeTabVal === 'localFiles'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
          >
            Add Local Files
          </button>
        </nav>
      </div>
      <div className="flex-1 max-h-[68vh] overflow-y-auto custom-scrollbar">
        <div className="max-w-[800px] mx-auto p-4 space-y-2">
          {activeTabVal === 'personal' && (
            <div>
              <div className="flex justify-center">
                {connectionStatus.loading ? (
                  <div className="w-64 h-16 flex items-center justify-center border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-4 h-4 border-2 border-gray-200 border-t-blue-500 rounded-full animate-spin" />
                      <span className="typography-body-sm text-gray-500">
                        Checking connection...
                      </span>
                    </div>
                  </div>
                ) : connectionStatus.isConnected ? (
                  <div className="w-full p-4 border border-gray-200 rounded-lg bg-white">
                    <div className="flex flex-col items-center space-y-3">
                      <div className="flex items-center space-x-2">
                        <Github className="w-6 h-6 text-gray-700" />
                        <CheckCircle2 className="w-5 h-5 text-green-500" />
                      </div>
                      <div className="text-center">
                        <p className="typography-body-sm font-weight-medium text-gray-900">
                          Connected to GitHub
                        </p>
                        <p className="typography-body-sm text-gray-500">
                          {connectionStatus.username}
                        </p>
                      </div>
                      <div className="flex flex-col w-full space-y-2">
                        <button
                          onClick={handleRepositoriesListClick}
                          className="w-full px-3 py-1.5 text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
                        >
                          Scan Repositories
                        </button>
                        <button
                          onClick={handleDisconnect}
                          className="w-full px-3 py-1.5 text-red-600 border border-red-200 rounded-md hover:bg-red-50 transition-colors"
                        >
                          Disconnect
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div>
                    <button
                      onClick={handleConnect}
                      disabled={connectionStatus.isConnected || is_free_user}
                      className={`flex items-center gap-2 px-4 py-2 rounded-md border border-gray-200 transition-colors ${is_free_user
                          ? 'opacity-50 cursor-not-allowed bg-gray-100'
                          : 'hover:bg-gray-50'
                        }`}
                    >
                      <Github className="w-5 h-5 text-gray-700" />
                      <span className="typography-body-sm font-weight-medium text-gray-800">
                        {is_free_user ? 'GitHub Connect (Premium Feature)' : 'Connect GitHub'}
                      </span>
                    </button>

                    {/* <button
                      className="flex items-center gap-2 px-4 py-2 hover:bg-gray-50 rounded-md border border-gray-200 transition-colors"
                    >
                      <Gitlab className="w-5 h-5 text-gray-700" />
                      <span className="typography-body-sm font-weight-medium text-gray-800">Connect GitLab</span>
                    </button> */}
                  </div>
                )}
              </div>

              {connectionStatus.error && (
                <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-md">
                  <XCircle className="w-5 h-5" />
                  <span className="typography-body-sm">{connectionStatus.error}</span>
                </div>
              )}

              <div className="relative">
                <div
                  className="absolute inset-0 flex items-center"
                  aria-hidden="true"
                >
                  <div className="w-full border-t border-gray-200" />
                </div>
                <div className="relative flex justify-center">
                  <span className="px-2 bg-white typography-body-sm text-gray-500">Or</span>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <Link className="w-6 h-6 text-gray-400" />
                  <h3 className="typography-body-sm font-weight-medium text-gray-900">
                    Import Public Repository{" "}
                  </h3>
                </div>
                <div className="space-y-4 flex flex-col gap-4">
                  <input
                    type="text"
                    placeholder="Repository URL (e.g., https://github.com/user/repo)"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md typography-body-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    value={gitUrl}
                    onChange={handleGitUrlChange}
                  />

                  {gitUrl && (
                    <div className="relative">
                      {showBranchDropdown &&
                        <div
                          className={`absolute border border-gray-500 w-full bg-white rounded-md z-20 max-h-[200px] overflow-y-scroll ${connectionStatus.isConnected ? 'bottom-[calc(100%+4px)]' : 'top-[calc(100%+4px)]'}`}
                        >
                          {isFetchingBranches ? (
                            <div className="text-gray-400 px-4 py-2">Loading Branches...</div>
                          ) :
                            branches.length === 0 ? (
                              <div className="text-gray-400 px-4 py-2">No branches found</div>
                            ) : (
                              <div>
                                {branches.length > 30 &&
                                  <form className="flex px-4 py-2 items-center justify-between gap-4" onSubmit={(e) => { checkBranchDetails(e) }}>
                                    <input className="border-gray-300 rounded-md flex-1" value={searchedBranch} type='text' placeholder="Type the branch name manually..." onChange={(e) => setSearchedBranch(e.target.value)} />
                                    {searchedBranch &&
                                      <DynamicButton
                                        variant="primaryLegacy"
                                        text= {isSearchingBranch ? "Searching..." : "Search"}
                                        type="submit"
                                        disabled={isSearchingBranch}
                                      />
                                    }
                                  </form>
                                }
                                {branches.map((branch, index) => (
                                  <p
                                    key={branch}
                                    className={`w-full px-4 py-2 bg-white text-black hover:bg-blue-500 hover:text-white hover:cursor-pointer 
                              ${index === 0 ? 'rounded-t-md' : index === branches.length - 1 ? 'rounded-b-md' : ''}`}
                                    onClick={() => handleSelectBranch(branch)}
                                  >
                                    {branch}
                                  </p>
                                ))}

                              </div>
                            )}
                        </div>
                      }
                      <div className={`w-full z-10 border border-gray-200 h-[30px] rounded-md flex justify-between px-4 items-center ${showBranchDropdown ? 'ring-2 ring-blue-500' : ''}`} onClick={showBranchDropdown? () => {setShowBranchDropdown((prev) => !prev)}  :  () => handleSelect()}>
                        <p>{selectedBranch ? selectedBranch : "Select Branch"}</p>
                        <ChevronDown className={`w-4 h-4 transition-transform ${showBranchDropdown ? 'rotate-180' : ''}`} />
                      </div>
                      {isFetchingBranches && (
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                        </div>
                      )}
                    </div>
                  )}
                  <button
                    className={`w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600 transition duration-300 flex items-center justify-center ${isCloning || !selectedBranch
                      ? "opacity-50 cursor-not-allowed"
                      : ""
                      }`}
                    onClick={handleImportGitUrl}
                    disabled={isCloning || !selectedBranch}
                  >
                    {isCloning ? "Cloning..." : "Import Url"}{" "}
                    <FiArrowRight className="ml-2" />
                  </button>
                  <p className="typography-caption text-gray-500 text-center">
                    Supports public repositories from GitHub
                  </p>
                </div>
              </div>
            </div>)}
          {activeTabVal === 'organization' && (
            <div className="max-w-lg mx-auto space-y-3">
              {scmConfigurations.filter(config => config.scm_type === 'github').length > 0 ? (
                scmConfigurations.map((config) => {
                  if (config.scm_type !== 'github') return null;

                  return (
                    <div
                      key={config.encrypted_scm_id}
                      className="group w-full p-4 text-left border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 transition-colors flex justify-between items-center"
                    >
                      <div className="flex items-center space-x-3">
                        <Github className="w-6 h-6 text-gray-700" />
                        <div>
                          <div className='flex items-center space-x-2'>
                            <p className="font-weight-medium text-gray-900">
                              {config.credentials.organization}
                            </p>
                            <span className="px-2 py-1 typography-caption font-weight-medium text-blue-600 bg-blue-50 rounded-full">
                              {config.scm_type.toUpperCase()}
                            </span>
                          </div>
                          <p className="typography-body-sm text-gray-500">
                            {config.api_url || 'Default API URL'}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleScanRepo(config.encrypted_scm_id)}
                        className={`px-4 py-1.5 rounded-lg typography-body-sm font-weight-medium transition-all flex items-center space-x-1
                ${scanningRepoId === config.encrypted_scm_id
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800'
                          }`}
                        disabled={scanningRepoId === config.encrypted_scm_id}
                      >
                        {scanningRepoId === config.encrypted_scm_id ? (
                          'Scanning...'
                        ) : (
                          <>
                            <RefreshCw className="w-4 h-4 mr-1" />
                            Scan Repo
                          </>
                        )}
                      </button>
                    </div>
                  );
                })
              ) : (
                <div className="text-center py-8">
                  <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-full">
                    <Code className="w-6 h-6 text-gray-400" />
                  </div>
                  <h3 className="mb-2 typography-body-lg font-weight-medium text-gray-900">
                    No GitHub Organization Accounts
                  </h3>
                  <p className="typography-body-sm text-gray-500">
                    Contact your administrator to set up GitHub organization accounts.
                  </p>
                </div>
              )}
            </div>
          )}
          {activeTabVal === 'localFiles' && (
            <div className="max-w-lg mx-auto space-y-3 flex flex-col">
              <div
                className={`w-full mx-auto border-2 border-dashed border-orange-300 gap-4 flex flex-col items-center justify-center bg-orange-50 rounded-md`}
                style={{ height: dropzoneHeight }}
                onDrop={handleDrop}
                onDragOver={handleDrag}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                ref={dropZoneRef}
              >
                <Upload size={28} className="text-orange-500" />

                <div>
                  <p className="text-center font-weight-medium">Drag and Drop Files Here </p>
                  <p className="text-center text-gray-500">or</p>
                </div>

                <DynamicButton
                  className="bg-white border-orange-500 text-orange-500 focus:ring-orange-500"
                  icon={FileUp}
                  text="Select Files"
                  onClick={handleFileInputClick}
                />
              </div>

              <input ref={fileInputRef} name="filesInput" className="hidden" type="file" multiple onChange={handleFileUpload} />

              {
                uploadedFiles.length > 0 && (
                  <div className="w-full flex flex-col gap-6">
                    <div className="w-full overflow-x-auto flex gap-2 pt-4">
                      {uploadedFiles.map((file, index) => (
                        <FileInfo file={file} onRemove={handleRemove} key={`uploadedFile-${index}`} />
                      ))}
                    </div>

                    <DynamicButton
                      variant="orange"
                      icon={Upload}
                      text="Upload Files"
                      className="self-center mb-4"
                      onClick={() => setShowIngestModal(true)}
                    />

                  </div>
                )
              }

              {showIngestModal && (
                <div className="fixed inset-0 flex items-center justify-center z-[60]">
                  <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setShowIngestModal(false)} />
                  <div className="bg-white rounded-lg shadow-xl w-lg p-8 z-[70] relative">
                    <div className="space-y-8">
                      <div className="text-center">
                        <h3 className="typography-body-lg font-weight-semibold text-gray-900">
                          Set Repository Name
                        </h3>
                        <p className="mt-2 typography-body-sm text-gray-500">
                          Provide a name for the repository
                        </p>
                      </div>

                      <div className="space-y-4">
                        <label className="block typography-body-sm font-weight-medium text-gray-700">
                          Repository Name
                        </label>
                        <input
                          type="text"
                          value={localProjectName}
                          onChange={(e) => setLocalProjectName(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none ring-2 ring-orange-500"
                          placeholder="Enter repository name"
                        />
                      </div>

                      {/* Action Buttons */}
                      <div className="grid grid-cols-2 gap-3 mt-6">
                        <button
                          onClick={() => setShowIngestModal(false)}
                          className="px-4 py-2 typography-body-sm font-weight-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none"
                        >
                          Cancel
                        </button>

                        <button
                          onClick={() => handleIngestFiles()}
                          className="px-4 py-2 typography-body-sm font-weight-medium text-white bg-red-600 hover:bg-red-700 rounded-md focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                          disabled={localProjectName == "" ? true : false}
                        >
                          Submit
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>

  );
}

export default PersonalRepositorySection;

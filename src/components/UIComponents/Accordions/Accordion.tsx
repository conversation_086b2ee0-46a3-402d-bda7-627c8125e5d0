import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Edit2,Figma } from 'lucide-react';
import { cn } from '@/lib/utils';

type AccordionMode = 'dynamic' | 'static';

interface AccordionProps {
    title: string;
    children: React.ReactNode;
    defaultOpen?: boolean;
    className?: string;
    preview?: React.ReactNode;
    mode?: AccordionMode;
    icon?: React.ReactNode;
    type?: string;
    onEdit?: () => void;
    showfigma?: boolean;
    handleOpenModal?: () => void;
    isLoading?: boolean;
    linkedFrames?: any[];
    figmaLink?: string;
}

export const Accordion: React.FC<AccordionProps> = ({
    title,
    children,
    defaultOpen = true,
    className = '',
    preview,
    mode = 'dynamic',
    icon,
    type = '',
    onEdit,
    showfigma = false,
    handleOpenModal,
    isLoading = false,
    linkedFrames = [],
    figmaLink = ''
}) => {
    const [isOpen, setIsOpen] = useState(defaultOpen);
    const isChildrenEmpty = !children || (typeof children === 'object' && Object.keys(children).length === 0) || children === '';
    const shouldShowContent = mode === 'static' || isOpen;

    const handleEditClick = (e: React.MouseEvent) => {
        e.stopPropagation();
        onEdit?.();
    };






  function generateIdFromTitle(title: string) {
    return title
        .toLowerCase() // Convert to lowercase
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/[^\w-]/g, ''); // Remove non-alphanumeric characters except hyphens
}




    return (
        <div
        id={generateIdFromTitle(title)}
        className={cn('border border-gray-200 rounded-lg mb-4', className)}>
            <button
                onClick={() => mode === 'dynamic' && setIsOpen(!isOpen)}
                className={cn(
                    'w-full flex items-center justify-between px-4 py-1 text-left rounded-t-lg transition-colors duration-200',
                    mode === 'dynamic' && 'hover:bg-gray-100',
                    mode === 'static' && 'cursor-default'
                )}
                type="button"
                aria-expanded={shouldShowContent}
                disabled={mode === 'static' && !onEdit}
            >
                <span className="flex items-center gap-2 font-weight-semibold typography-body text-[#2A3439]">
                    {icon && <span className="flex-shrink-0">{icon}</span>}
                    {title}
                </span>
                <div className="flex items-center gap-4">
                <div
  onClick={handleEditClick}
  className="flex items-center justify-center h-8 w-8 text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md transition-all duration-200 cursor-pointer"
  title="Edit"
  aria-label="Edit"
  role="button"
  tabIndex={0}
  onKeyDown={(e) => {
    if (e.key === 'Enter') {
      e.stopPropagation();
      onEdit?.();
    }
  }}
>
  <Edit2 size={16} />
</div>

                    {showfigma && handleOpenModal && (
                        <div
                            onClick={(e) => {
                                e.stopPropagation();
                                handleOpenModal();
                            }}
                            role="button"
                            tabIndex={0}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    e.stopPropagation();
                                    handleOpenModal();
                                }
                            }}
                            className={cn(
                                'flex items-center gap-2 px-3 py-1 typography-body-sm rounded-md transition-colors duration-200',
                                 'text-purple-600 bg-white hover:bg-purple-50 border border-gray-200 hover:border-purple-300 cursor-pointer'
                            )}
                        >
                            {/* Replace Figma with the appropriate icon */}
                            <span className="text-purple-600"><Figma className="size-4 flex-shrink-0"  /></span>
                            <span>
                                {isLoading
                                    ? 'Loading...'
                                    : (linkedFrames.length > 0 ? 'Update' : 'Link') + ' Figma Elements'}
                            </span>
                        </div>
                    )}
                    {mode === 'dynamic' && (
                        <>
                            <div className="h-5 w-px bg-gray-300"></div>
                            <span className="text-gray-500">
                                {shouldShowContent ?
                                    <ChevronUp className="h-5 w-5" /> :
                                    <ChevronDown className="h-5 w-5" />
                                }
                            </span>
                        </>
                    )}
                </div>
            </button>

            {!shouldShowContent && preview && (
                <div className="px-4 py-1.5 bg-white border-t border-gray-200 rounded-b-lg typography-body-sm font-weight-medium text-[#464F60]">
                    {preview}
                </div>
            )}

            {shouldShowContent && (
                <div

                className="p-4 bg-white border-t border-gray-200 rounded-b-lg typography-body-sm text-[#464F60] font-weight-medium">
                    {isChildrenEmpty ? 'No Details found' : children}
                </div>
            )}
        </div>
    );
};

export default Accordion;

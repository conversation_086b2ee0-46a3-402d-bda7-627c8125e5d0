import React, { useState } from "react";
import Badge from "@/components/UIComponents/Badge/Badge";
import { Database } from "lucide-react";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";

/**
 * DatabaseDetails component displays information about a database
 * 
 * @param {Object} props
 * @param {Object} props.databaseInfo - The database information object
 * @param {boolean} props.loading - Whether the database information is loading
 * @param {boolean} props.hasDatabase - Whether the container has a database property set to true
 */
const DatabaseDetails = ({ databaseInfo, loading, hasDatabase }) => {
  const [expandedSections, setExpandedSections] = useState({});

  const toggleSection = (sectionId, dbId) => {
    setExpandedSections(prev => ({
      ...prev,
      [`${sectionId}_${dbId}`]: !prev[`${sectionId}_${dbId}`]
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8 border rounded-md bg-white">
        <div className="inline-block w-6 h-6 border-2 border-t-transparent border-blue-500 rounded-full animate-spin mr-3"></div>
        <p>Loading database information...</p>
      </div>
    );
  }

  if (!databaseInfo || databaseInfo.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 border rounded-md bg-white">
        <Database size={32} className="text-gray-400 mb-2" />
        <p className="text-gray-600">No database found for this container</p>
        {hasDatabase && (
          <p className="text-sm text-gray-500 mt-1">
            You can configure a database by clicking the "Database Config" button
          </p>
        )}
      </div>
    );
  }

  // Define metadata for database properties
  const dbMetadata = {
    Title: { display_type: "text" },
    DatabaseName: { display_type: "text" },
    DatabaseType: { display_type: "text" },
    DatabaseVersion: { display_type: "text" },
    Host: { display_type: "text" },
    Port: { display_type: "text" },
    SchemaName: { display_type: "text" },
    Username: { display_type: "text" },
    AuthenticationType: { display_type: "text" },
    SSLEnabled: { display_type: "text" },
    ConnectionPoolSize: { display_type: "text" },
    QueryTimeout: { display_type: "text" },
    Description: { display_type: "rich_text" },
    DatabaseSchema: { display_type: "rich_text" },
    EntityRelationshipDiagram: { 
      display_type: "type_toggle", 
      related_field: "EntityDefinitions" 
    },
    EntityDefinitions: { 
      display_type: "rich_text"
    },
    RelationshipMappings: { display_type: "rich_text" }
  };

  return (
    <div className="border rounded-md p-4 bg-white">
      {databaseInfo.map((db) => (
        <div key={db.id} className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-md font-medium">{db.properties.Title || db.properties.DatabaseName}</h4>
            <Badge type="Database" />
          </div>
          
          <PropertiesRenderer 
            properties={db.properties}
            metadata={dbMetadata}
            to_skip={["DatabaseName", "QueryTimeout", "SSLEnabled", "AuthenticationType", "Host", "Username", "PasswordRef", "ConnectionPoolSize",
              "DatabaseVersion","SchemaName","Port", "EntityDefinitions"
             ]}
          />
          
        </div>
      ))}
    </div>
  );
};

export default DatabaseDetails; 
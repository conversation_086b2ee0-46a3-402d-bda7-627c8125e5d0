// components/NotificationPermission.tsx
import { useNotifications } from '@/components/Context/NotificationProvider';
import Cookies from 'js-cookie';

export const NotificationPermission = () => {
  const { isLoading, permissionStatus } = useNotifications();
  const idToken = Cookies.get('idToken');

  if (!idToken) {
    return null; // Don't show anything if user is not logged in
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-custom-border" />
      </div>
    );
  }

  if (permissionStatus === 'granted') {
    return (
      <span className="inline-flex items-center bg-custom-bg-muted text-custom-text-success border border-custom-border px-3 py-1 typography-body-sm font-weight-medium rounded-full">
        ✅ Notifications enabled successfully!
      </span>
    );
  }
  
  if (permissionStatus === 'denied') {
    return (
      <span className="inline-flex items-center bg-red-100 text-red-700 border border-red-400 px-3 py-1 typography-body-sm font-weight-medium rounded-full">
        ❌ Please enable notifications in your browser settings to receive updates.
      </span>
    );
  }
  

  return (
    <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
      <p className="mb-2">Would you like to receive notifications?</p>
      <button
       onClick={() => {
        Notification.requestPermission().then(() => {
          window.location.reload();
        });
      }}
        className="bg-blue-500 hover:bg-blue-700 text-white font-weight-bold py-2 px-4 rounded"
      >
        Enable Notifications
      </button>
    </div>
  );
};
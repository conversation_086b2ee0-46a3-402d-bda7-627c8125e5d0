// Modified StatusDisplay.js
import React from 'react';
import { FaFileAlt, FaSync, FaRegTrashAlt, FaStop } from 'react-icons/fa';
import Badge from "../UIComponents/Badge/Badge";
import { BootstrapTooltip } from "../UIComponents/ToolTip/Tooltip-material-ui";
import ConnectionIndicator from "../ConnectionIndicator/ConnectionIndicator";
import CustomDropdown from "../UIComponents/Dropdowns/CustomDropdown";
import { formatDateTime } from '@/utils/datetime';

const StatusDisplay = ({
  taskData,
  isConnected = false,
  handleRefresh,
  confirmAndDelete,
  handleStopTask,
  menuOptions = [],
  autoNavigateEnabled = false, // New prop for toggle state
  setAutoNavigateEnabled = () => {} // New prop for toggle function
}) => {
  const {
    task_id: taskId,
    title,
    progress = 0,
    status: taskStatus = 'idle',
    start_time: lastTime,
    task_type: configLabel = 'auto-config'
  } = taskData || {};

  const ProgressBar = ({ progress }) => {
    // Determine color based on progress
    const getProgressColor = () => {
      if (progress < 70) return 'bg-primary';
      return 'bg-success';
    };

    return (
      <div className="w-48 bg-custom-bg-muted rounded-full h-2.5 overflow-hidden border border-custom-border shadow-inner">
        <div
          className={`h-2.5 rounded-full transition-all duration-300 ${getProgressColor()}`}
          style={{ width: `${progress}%` }}
        ></div>
      </div>
    );
  };

  return (
    <div className="w-full bottom-0 flex justify-between p-4 mt-3 bg-white rounded-md border border-gray-200 shadow-sm">
      <div>
        <div className="flex items-center mb-2">
          {taskStatus.toLowerCase() !== 'idle' && (
            <h3 className="typography-body font-weight-semibold text-gray-800">{title}</h3>
          )}
        </div>
        <div className="typography-body-sm text-gray-600 flex items-center gap-3 mb-1.5">
          <div className="flex items-center gap-1.5">
            <span className="font-weight-medium">Task ID:</span>
            <span className="">{taskId}</span>
          </div>

          <a
            href={`https://us5.datadoghq.com/logs?query=task_id%3A${taskId}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 flex items-center gap-1"
          >
            <FaFileAlt size={14} />
            <span className="typography-body-sm">View Logs</span>
          </a>
          <div>
            <Badge type={
              configLabel === "auto-extract" ? "Auto Extraction" :
              configLabel === "re-config" ? "Re-Configure" :
              "Auto Configure"
            } />
          </div>
        </div>
        {lastTime && (
          <div className="typography-body-sm text-gray-500">
            Updated at: {formatDateTime(lastTime, true)}
          </div>
        )}
      </div>

      <div className="flex items-center space-x-3">
        <div className="flex flex-col items-center mr-2">
          <ProgressBar progress={progress} />
          <span className="typography-caption text-gray-500 mt-1">{progress}% Complete</span>
        </div>

        <span className={`px-2.5 py-1 rounded-md inline-flex items-center ${taskStatus.toLowerCase() === 'complete' ? 'bg-green-100 text-green-800' :
          taskStatus.toLowerCase() === 'cancelled' ? 'bg-red-100 text-red-800' :
            taskStatus.toLowerCase() === 'in_progress' ? 'bg-primary-100 text-primary-800' :
              'bg-gray-100 text-gray-800'
          }`}>
          <span className={`w-1.5 h-1.5 rounded-full mr-1.5 ${taskStatus.toLowerCase() === 'complete' ? 'bg-green-500' :
            taskStatus.toLowerCase() === 'cancelled' ? 'bg-red-500' :
              taskStatus.toLowerCase() === 'in_progress' ? 'bg-primary-500' :
                'bg-gray-500'
            }`}></span>
          {taskStatus.toLowerCase() === 'in_progress' ? 'In Progress' :
            taskStatus.charAt(0).toUpperCase() + taskStatus.slice(1)}
        </span>

        <BootstrapTooltip
          id={`auto-config-indicator-${taskId}`}
          content={isConnected ? "Connected" : "Disconnected"}
        >
          <ConnectionIndicator isConnected={isConnected} />
        </BootstrapTooltip>

        <div className="flex items-center gap-1.5">
          <BootstrapTooltip
            id={`refresh-task-${taskId}`}
            content="Refresh the status panel"
          >
            <button
              className="p-2 rounded-md border border-gray-200 bg-white hover:bg-gray-50 transition-colors duration-200"
              onClick={handleRefresh}
            >
              <FaSync className="text-gray-600" size={14} />
            </button>
          </BootstrapTooltip>

          <BootstrapTooltip
            id={`delete-task-${taskId}`}
            content={`Delete the task and clear the execution status panel`}
          >
            <button
              className="p-2 rounded-md border border-gray-200 bg-white hover:bg-gray-50 transition-colors duration-200"
              onClick={confirmAndDelete}
            >
              <FaRegTrashAlt className="text-gray-600" size={14} />
            </button>
          </BootstrapTooltip>

          <BootstrapTooltip
            id={`stop-task-${taskId}`}
            content={`Stop the execution of the task`}
          >
            <button
              className="p-2 rounded-md border border-gray-200 bg-white hover:bg-gray-50 transition-colors duration-200"
              onClick={handleStopTask}
            >
              <FaStop className="text-red-500" size={14} />
            </button>
          </BootstrapTooltip>

          <CustomDropdown options={menuOptions} align="right" />
        </div>
      </div>
    </div>
  );
};

export default StatusDisplay;
// app/dashboard/groups/page.tsx
"use client";

import React, { useContext, useEffect, useState } from "react";
import Search from "@/components/BrowsePanel/TableComponents/Search";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import CreateGroupModal from "@/components/UserOnboarding/Modal/CreateGroup";
import { useRouter } from "next/navigation";
import { Plus, Trash2 } from "lucide-react";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { fetchOrganizationGroups, deleteGroup } from "@/utils/api";
import Loader from "@/components/UserOnboarding/ui/Loader";
import { useUser } from "@/components/Context/UserContext";
import Cookies from "js-cookie";
import DeleteConfirmationModal from "@/components/Modal/DeleteConfirmationModal";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

interface GroupDetailState {
  name: string;
  type: string;
  description: string;
  accessLevel: string;
}

interface PermissionState {
  projectDetails: PermissionActions;
  workItems: PermissionActions;
  requirements: PermissionActions;
  architectureDesign: PermissionActions;
  prototype: PermissionActions;
  development: PermissionActions;
  documentation: PermissionActions;
}

interface PermissionActions {
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
  merge: boolean;
}

// Array of badge colors for group types
const typeColors = [
  "bg-semantic-blue-100 text-semantic-blue-800",
  "bg-semantic-green-100 text-semantic-green-800",
  "bg-semantic-purple-100 text-semantic-purple-800",
  "bg-semantic-red-100 text-semantic-red-800",
  "bg-semantic-yellow-100 text-semantic-yellow-800",
  "bg-semantic-blue-100 text-semantic-blue-800",
  "bg-semantic-red-100 text-semantic-red-800",
  "bg-semantic-green-100 text-semantic-green-800",
  "bg-primary-100 text-primary-800",
  "bg-semantic-blue-100 text-semantic-blue-800",
];

export default function Page() {
  const router = useRouter();
  const [groups, setGroups] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 9;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [groupToDelete, setGroupToDelete] = useState<string | null>(null);
  const {showAlert} = useContext(AlertContext);
  const [isDeleting, setIsDeleting] = useState(false);

  const {is_admin, tenant_id, fetchUserData, is_updated} = useUser();
  if(!is_updated) {
    fetchUserData();
  }


  const fetchGroups = async () => {
    setIsLoading(true);
    const orgId = tenant_id || Cookies.get("tenant_id");
    try {
      const response = await fetchOrganizationGroups(orgId);
      setGroups(response);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle successful group creation
  const handleGroupCreated = () => {
    setIsModalOpen(false);
    fetchGroups(); // Refresh the groups list
  };

  useEffect(() => {
    fetchGroups();
  }, []);


  const filteredGroups = React.useMemo(() => {
    return groups.filter((group) =>
      Object.values(group)
        .join(" ")
        .toLowerCase()
        .includes(searchTerm.toLowerCase())
    );
  }, [groups, searchTerm]);

  const totalPages = Math.ceil(filteredGroups.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentGroups = filteredGroups.slice(startIndex, endIndex);

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  const handleGroupClick = (groupId: string) => {
    router.push(`/dashboard/groups/${groupId}/overview`);
  };

  const getRandomColorClass = (groupType: string) => {
    // Create a consistent index based on the group type
    const index = groupType
      .split("")
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return typeColors[index % typeColors.length];
  };

  const handleDeleteGroup = async (groupId: string) => {
    const orgId = tenant_id || Cookies.get("tenant_id");

    setIsDeleting(true);

    try {
      const result = await deleteGroup(orgId, groupId);

      if (result.success) {
        setGroups(groups.filter(group => group._id !== groupId));
        setIsDeleteModalOpen(false);
        setGroupToDelete(null);
        showAlert("Group deleted successfully", "success");
      }
    } catch (error) {

      showAlert("Failed to delete group", "error");
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="">
        <Loader type="groups" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-600">
        Error loading groups: {error.message}
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-80px)] flex flex-col">
      {/* Search Header */}
      <div className="bg-semantic-gray-50 px-2 py-4 border-b border-custom-border">
        <div className="max-w-[1200px] mx-auto">
          <div className="typography-body-lg font-weight-semibold text-custom-text-primary -mt-2 mb-2">
            Groups
          </div>
          <div className="flex justify-between items-center">
            <div className="">
              <Search searchTerm={searchTerm} setSearchTerm={handleSearch} />
            </div>
            <DynamicButton
              variant="primary"
              icon={Plus}
              text="Create Group"
              onClick={() => setIsModalOpen(true)}
            />
            <CreateGroupModal
              isOpen={isModalOpen}
              onClose={() => setIsModalOpen(false)}
              orgId="org-e29c4e53"
              onSuccess={handleGroupCreated}
              // onSubmit={handleCreateGroup}
            />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto bg-gray-50 px-2 py-4 custom-scrollbar">
        <div className="max-w-[1200px] mx-auto">
          {currentGroups.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {currentGroups.map((group) => (
                <div
                  key={group._id}
                  className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:border-blue-500 transition-all duration-200"
                >
                  <div className="flex items-center justify-between mb-3">
                    <span
                      className={`px-2 py-1 rounded-md typography-caption ${getRandomColorClass(
                        group.group_type
                      )}`}
                    >
                      {group.group_type}
                    </span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setGroupToDelete(group._id);
                        setIsDeleteModalOpen(true);
                      }}
                      className="p-1.5 hover:bg-red-50 rounded-full text-gray-400 hover:text-red-500 transition-colors"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>

                  <div onClick={() => handleGroupClick(group._id)} className="cursor-pointer">
                    <h3 className="typography-body-lg font-weight-semibold text-gray-900 mb-2">
                      {group.name}
                    </h3>

                    <p className="typography-body-sm text-gray-600 mb-4">
                      {group.description}
                    </p>

                    <div className="flex items-center justify-between typography-body-sm text-gray-500">
                      <div className="flex items-center">
                        <svg
                          className="w-4 h-4 mr-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                          />
                        </svg>
                        {group.users?.length || 0}{" "}
                        {(group.users?.length || 0) > 1 ? "members" : "member"}
                      </div>
                      {/* <span>{group.access_level}</span> */}
                      <span>
                        {new Date(group.created_at).toLocaleDateString("en-US", {
                          year: "numeric",
                          month: "short",
                          day: "numeric",
                        })}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : searchTerm ? (
            <div className="flex justify-center items-center h-full min-h-[400px]">
              <EmptyStateView
                type="noSearchResult"
                onClick={() => {
                  setSearchTerm("");
                  setCurrentPage(1);
                }}
              />
            </div>
          ) : (
            <div className="flex justify-center items-center h-full min-h-[400px]">
              <EmptyStateView
                type="noGroups"
                onClick={() => router.push("/dashboard/groups/create")}
              />
            </div>
          )}
        </div>
      </div>

      {/* Pagination Footer */}
      {/* [Previous pagination code remains the same] */}
      {filteredGroups.length > 0 && (
        <div className="bg-gray-50 px-2 py-4 border-t">
          <div className="max-w-[1200px] mx-auto">
            <div className="flex items-center justify-between">
              <span className="typography-body-sm text-gray-600">
                Showing {startIndex + 1}-
                {Math.min(endIndex, filteredGroups.length)} of{" "}
                {filteredGroups.length}
              </span>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-2 py-1 text-gray-600 disabled:text-gray-400"
                >
                  ←
                </button>
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  if (totalPages <= 5) {
                    return i + 1;
                  }
                  if (currentPage <= 3) {
                    return i + 1;
                  }
                  if (currentPage >= totalPages - 2) {
                    return totalPages - 4 + i;
                  }
                  return currentPage - 2 + i;
                }).map((page) => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-2 py-1 rounded min-w-[32px] ${
                      currentPage === page
                        ? "bg-blue-500 text-white"
                        : "text-gray-600 hover:bg-gray-100"
                    }`}
                  >
                    {page}
                  </button>
                ))}
                {totalPages > 5 && currentPage < totalPages - 2 && (
                  <>
                    <span className="px-2">...</span>
                    <button
                      onClick={() => setCurrentPage(totalPages)}
                      className="px-2 py-1 rounded min-w-[32px] text-gray-600 hover:bg-gray-100"
                    >
                      {totalPages}
                    </button>
                  </>
                )}
                <button
                  onClick={() =>
                    setCurrentPage(Math.min(totalPages, currentPage + 1))
                  }
                  disabled={currentPage === totalPages}
                  className="px-2 py-1 text-gray-600 disabled:text-gray-400"
                >
                  →
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setGroupToDelete(null);
        }}
        onConfirm={() => {
          if (groupToDelete) {
            handleDeleteGroup(groupToDelete);
          }
        }}
        isLoading={isDeleting}
        title="Delete Group"
        message="Are you sure you want to delete this group? This action cannot be undone."
      />
    </div>
  );
}

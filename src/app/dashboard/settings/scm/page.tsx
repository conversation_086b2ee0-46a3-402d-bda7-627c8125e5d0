"use client";

import React, { useEffect, useState } from "react";
import { Github, Search, Gitlab } from "lucide-react";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { getScmConfiguration } from "@/utils/api";
import { useRouter } from "next/navigation";

interface Provider {
  id: "github" | "gitlab";
  name: string;
  icon: React.ElementType;
  description: string;
  features: string[];
  popularity: string;
  color: string;
  connectionCount: number;
}

interface SCMConfiguration {
  scm_type: string;
  credentials: {
    auth_type: string;
    organization: string;
  };
  encrypted_scm_id: string;
}

const Page: React.FC = () => {
  const [selectedProvider, setSelectedProvider] = useState<
    "github" | "gitlab" | null
  >(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [configurations, setConfigurations] = useState<SCMConfiguration[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const providers: Provider[] = [
    {
      id: "github",
      name: "GitHub",
      icon: Github,
      description: "Connect and manage your GitHub repositories",
      features: [
        "Public/Private Repos",
        "Branch Protection",
        "Team Management",
      ],
      popularity: "Most Popular",
      color: "bg-semantic-gray-900",
      connectionCount: 0,
    },
    {
      id: "gitlab",
      name: "GitLab",
      icon: Gitlab,
      description: "Connect and manage your GitLab repositories",
      features: ["CI/CD Integration", "Container Registry", "Wiki"],
      popularity: "Enterprise Ready",
      color: "bg-primary",
      connectionCount: 0,
    },
  ];

  const getConfiguration = async () => {
    try {
      const response = await getScmConfiguration();
      if (response?.data?.configurations) {
        const updatedProviders = providers.map((provider) => ({
          ...provider,
          connectionCount: response.data.configurations.filter(
            (config: SCMConfiguration) => config.scm_type === provider.id
          ).length,
        }));
        setConfigurations(response.data.configurations);
        setProvidersData(updatedProviders);
      }
    } catch (error) {
      
    } finally {
      setIsLoading(false);
    }
  };

  const [providersData, setProvidersData] = useState(providers);

  useEffect(() => {
    getConfiguration();
  }, []);

  const filteredProviders = providersData.filter((provider) =>
    provider.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleProviderSelect = (providerId: "github" | "gitlab") => {
    setSelectedProvider(providerId);
    router.push(`/dashboard/settings/scm/${providerId}`);
    sessionStorage.setItem("providerId", providerId);
  };

  const handleClearSearch = () => {
    setSearchQuery("");
  };

  const ConnectionCountBadge = ({ count }: { count: number }) => {
    if (isLoading) {
      return (
        <span className="typography-caption bg-gray-100 text-gray-700 px-2 py-1 rounded-full flex items-center space-x-1">
          <span className="inline-flex">
            <span
              className="h-1 w-1 bg-gray-500 rounded-full animate-bounce"
              style={{ animationDelay: "0ms" }}
            ></span>
            <span
              className="h-1 w-1 bg-gray-500 rounded-full animate-bounce mx-1"
              style={{ animationDelay: "150ms" }}
            ></span>
            <span
              className="h-1 w-1 bg-gray-500 rounded-full animate-bounce"
              style={{ animationDelay: "300ms" }}
            ></span>
          </span>
        </span>
      );
    }

    return (
      <span className="typography-caption bg-gray-100 text-gray-700 px-2 py-1 rounded-full">
        {count} {count === 1 ? "Connection" : "Connections"}
      </span>
    );
  };

  return (
    <div className="w-full max-w-6xl p-8 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="typography-heading-4 font-weight-semibold text-gray-900 -mt-2 mb-2">
            Source Control Integration
          </h1>
          <p className="text-gray-500 mt-2">
            Configure your development workflow with enterprise-grade SCM
            solutions
          </p>
        </div>
      </div>

      {!selectedProvider && (
        <>
          <div className="relative">
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <Search className="text-gray-400 w-5 h-5" />
            </div>
            <input
              type="text"
              placeholder="Search providers..."
              className="w-full pl-12 pr-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {filteredProviders.length > 0 ? (
              filteredProviders.map((provider) => {
                const Icon = provider.icon;
                return (
                  <div
                    key={provider.id}
                    className="bg-white rounded-lg shadow-sm border p-6 transform transition-all duration-300 hover:scale-[1.02] cursor-pointer hover:shadow-md"
                    onClick={() => handleProviderSelect(provider.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div
                          className={`p-3 rounded-lg ${provider.color} text-white`}
                        >
                          <Icon className="w-6 h-6" />
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <h3 className="font-weight-semibold typography-heading-4">
                              {provider.name}
                            </h3>
                            <span className="typography-caption bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                              {provider.popularity}
                            </span>
                            <ConnectionCountBadge
                              count={provider.connectionCount}
                            />
                          </div>
                          <p className="text-gray-500 mt-1">
                            {provider.description}
                          </p>
                          <div className="flex flex-wrap gap-2 mt-3">
                            {provider.features.map((feature, index) => (
                              <span
                                key={index}
                                className="typography-caption bg-gray-100 text-gray-700 px-2 py-1 rounded-full"
                              >
                                {feature}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : filteredProviders.length === 0 && searchQuery ? (
              <div className="flex items-center justify-center w-full h-full">
                <EmptyStateView
                  type="noSearchResult"
                  onClick={handleClearSearch}
                />
              </div>
            ) : (
              <div className="flex items-center justify-center w-full h-full">
                <EmptyStateView type="noScmProviders" onClick={() => {}} />
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default Page;

'use client';

import React, { useEffect, useState } from 'react';
import { Building, User, CreditCard } from 'lucide-react';
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import DetailsSection from "@/components/UserOnboarding/PendingApproval/DetailsSection";
import { createOrganization, fetchOrganizationPlans } from '@/utils/api';
import Cookies from 'js-cookie';

interface ReviewConfirmProps {
  onBack: () => void;
  onConfirm: () => void;
}

interface OrganizationData {
  name: string;
  business_email: string;
  industrial_type: string;
  company_size: string;
  domain: string;
  image: string;
  plan_id: string;
  admin_name: string;
  admin_email: string;
  admin_contact_number: string;
  admin_department: string;
  configurations: {
    max_users: number;
    role_customization: boolean;
    api_access: boolean;
    github_integration: boolean;
    jira_integration: boolean;
    custom_reports: boolean;
    export_capabilities: boolean;
  };
  tenant_username: string;
}

interface APIPlan {
  id: string;
  name: string;
  price: number;
  interval: 'monthly' | 'yearly';
  features: {
    workflow_templates: string;
    workflow_management: string;
    security: string;
    workflow_analysis: boolean;
    custom_workflow: boolean;
    integration: string | boolean;
    support: string | boolean;
    developer_platform: boolean;
  };
}

const COOKIE_NAME = 'org_details_form';

const ReviewConfirm: React.FC<ReviewConfirmProps> = ({ onBack, onConfirm }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [organizationDetails, setOrganizationDetails] = useState<OrganizationData | null>(null);
  const [planDetails, setPlanDetails] = useState<APIPlan | null>(null);
  const [isCreatingOrg, setIsCreatingOrg] = useState<boolean>(false);
  const [isOrganizationCreated, setIsOrganizationCreated] = useState(false);

  useEffect(() => {
    const loadData = async () => {
      const cookieData = Cookies.get(COOKIE_NAME);
      
      if (cookieData) {
        try {
          const parsedData = JSON.parse(cookieData);
          const transformedData = {
            tenant_username: parsedData.username || 'Not provided',
            name: parsedData.name || 'Not provided',
            business_email: parsedData.business_email || `contact@${parsedData.domain || ''}`,
            industrial_type: parsedData.industrial_type || 'Not provided',
            company_size: parsedData.company_size || 'Not provided',
            domain: parsedData.domain || 'Not provided',
            image: parsedData.image || '',
            plan_id: parsedData.plan_id || 'Not provided',
            admin_name: parsedData.admin_name || 'Not provided',
            admin_email: parsedData.work_email || 'Not provided',
            admin_contact_number: parsedData.contact_number || 'Not provided',
            admin_department: parsedData.department || 'Not provided',
            configurations: parsedData.configurations || {
              max_users: 2,
              role_customization: false,
              api_access: false,
              github_integration: false,
              jira_integration: false,
              custom_reports: false,
              export_capabilities: false
            }
          };

          setOrganizationDetails(transformedData);

          if (parsedData.plan_id) {
            const plans = await fetchOrganizationPlans();
            const selectedPlan = plans.find((plan: APIPlan) => plan.id === parsedData.plan_id);
            if (selectedPlan) {
              setPlanDetails(selectedPlan);
            }
          }
        } catch (error) {
          
          setError('Error loading organization details');
        }
      } else {
        setError('No organization details found in cookies');
      }
    };

    loadData();
  }, []);

  const handleConfirm = async () => {
    if (!organizationDetails) {
      setError('No organization details found');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      setIsCreatingOrg(true);
      await createOrganization(organizationDetails);
      // Clear cookie after successful creation
      Cookies.remove(COOKIE_NAME);
      setIsOrganizationCreated(true);
      await new Promise(resolve => setTimeout(resolve, 3000));
      onConfirm();
      await new Promise(resolve => setTimeout(resolve, 200));
      window.location.href = '/dashboard/organizations';
      await new Promise(resolve => setTimeout(resolve, 200));
    } catch (error) {
      
      setError('Failed to create organization');
    } finally {
      setIsLoading(false);
      setIsCreatingOrg(false);
    }
  };

  const formatValue = (value: any): string => {
    if (value === undefined || value === null || value === '') {
      return 'Not provided';
    }
    return String(value);
  };

  if (!organizationDetails) {
    return (
      <div className="p-3 animate-pulse">
        <div className="h-8 bg-gray-200 rounded-md w-48 mb-8"></div>
        
        <div className="space-y-6">
          {/* Organization Details Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center gap-2 mb-4">
              <div className="h-5 w-5 bg-gray-200 rounded"></div>
              <div className="h-6 bg-gray-200 rounded w-40"></div>
            </div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex justify-between">
                  <div className="h-4 bg-gray-200 rounded w-32"></div>
                  <div className="h-4 bg-gray-200 rounded w-48"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Admin Setup Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center gap-2 mb-4">
              <div className="h-5 w-5 bg-gray-200 rounded"></div>
              <div className="h-6 bg-gray-200 rounded w-32"></div>
            </div>
            <div className="space-y-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="flex justify-between">
                  <div className="h-4 bg-gray-200 rounded w-32"></div>
                  <div className="h-4 bg-gray-200 rounded w-48"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Selected Plan Section */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center gap-2 mb-4">
              <div className="h-5 w-5 bg-gray-200 rounded"></div>
              <div className="h-6 bg-gray-200 rounded w-36"></div>
            </div>
            <div className="space-y-4">
              {[...Array(2)].map((_, i) => (
                <div key={i} className="flex justify-between">
                  <div className="h-4 bg-gray-200 rounded w-32"></div>
                  <div className="h-4 bg-gray-200 rounded w-48"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isCreatingOrg) {
    return (
      <div className="w-full h-full flex items-center justify-center py-16">
        <div className="flex flex-col items-center gap-6">
          {isOrganizationCreated ? (
            <div className="w-8 h-8 text-green-500">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                <path fillRule="evenodd" d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12zm13.36-1.814a.75.75 0 10-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 00-1.06 1.06l2.25 2.25a.75.75 0 001.14-.094l3.75-5.25z" clipRule="evenodd" />
              </svg>
            </div>
          ) : (
            <div className="w-64 h-2 bg-gray-100 rounded-full overflow-hidden">
              <div className="h-full bg-gradient-to-r from-blue-400 to-blue-600 rounded-full animate-loading-bar" />
            </div>
          )}

          <div className="flex flex-col items-center text-center">
            <div className="typography-heading-4 font-weight-semibold text-gray-900">
              {isOrganizationCreated ? "Organization Created Successfully" : "Creating Your Organization"}
            </div>
            <div className="typography-body-sm text-gray-600 mt-2">
              {isOrganizationCreated ? "Redirecting to dashboard..." : "Setting up your workspace and configuring features..."}
            </div>
            <div className="typography-caption text-gray-500 mt-1">
              {isOrganizationCreated ? "" : "This may take a few moments"}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-3">
      <h1 className="project-panel-heading mb-8">Review & Confirm</h1>

      {error && (
        <div className="text-red-500 mb-4">
          {error}
        </div>
      )}

      <div className="space-y-6 max-h-[60vh] overflow-y-auto pr-4">
        <DetailsSection
          title="Organisation Details"
          icon={Building}
          details={[
            { label: "Organization Name", value: formatValue(organizationDetails?.name) },
            { label: "Organization ID", value: formatValue(organizationDetails?.tenant_username) },
            { label: "Industry Type", value: formatValue(organizationDetails?.industrial_type) },
            { label: "Company Size", value: formatValue(organizationDetails?.company_size) },
            { label: "Domain", value: formatValue(organizationDetails?.domain) }
          ]}
        />

        <DetailsSection
          title="Admin Setup"
          icon={User}
          details={[
            { label: "Primary Admin", value: formatValue(organizationDetails?.admin_name) },
            { label: "Email", value: formatValue(organizationDetails?.admin_email) },
            { label: "Contact Number", value: formatValue(organizationDetails?.admin_contact_number) },
            { label: "Department", value: formatValue(organizationDetails?.admin_department) }
          ]}
        />

        <DetailsSection
          title="Selected Plan"
          icon={CreditCard}
          details={[
            { label: "Plan Name", value: formatValue(planDetails?.name) },
            { label: "Price", value: planDetails ? `$${planDetails.price} USD per ${planDetails.interval}` : 'Not provided' },
            { label: "Workflow Templates", value: formatValue(planDetails?.features.workflow_templates) },
            { label: "Workflow Management", value: formatValue(planDetails?.features.workflow_management) },
            { label: "Security Features", value: formatValue(planDetails?.features.security) },
            { label: "Workflow Analysis", value: planDetails?.features.workflow_analysis ? "Included" : "Not included" },
            { label: "Custom Workflow", value: planDetails?.features.custom_workflow ? "Included" : "Not included" },
            { label: "Integration", value: typeof planDetails?.features.integration === 'string' ? planDetails.features.integration : planDetails?.features.integration ? "Available" : "Not available" },
            { label: "Support", value: typeof planDetails?.features.support === 'string' ? planDetails.features.support : planDetails?.features.support ? "Available" : "Not available" },
            { label: "Developer Platform", value: planDetails?.features.developer_platform ? "Included" : "Not included" }
          ]}
        />

        {organizationDetails?.configurations && (
          <DetailsSection
            title="Feature Configuration"
            icon={CreditCard}
            details={[
              { 
                label: "Max Users", 
                value: formatValue(organizationDetails.configurations.max_users)
              },
              { 
                label: "Role Customization", 
                value: organizationDetails.configurations.role_customization ? "Enabled" : "Disabled"
              },
              { 
                label: "API Access", 
                value: organizationDetails.configurations.api_access ? "Enabled" : "Disabled"
              },
              { 
                label: "GitHub Integration", 
                value: organizationDetails.configurations.github_integration ? "Enabled" : "Disabled"
              },
              { 
                label: "Jira Integration", 
                value: organizationDetails.configurations.jira_integration ? "Enabled" : "Disabled"
              },
              { 
                label: "Custom Reports", 
                value: organizationDetails.configurations.custom_reports ? "Enabled" : "Disabled"
              },
              { 
                label: "Export Capabilities", 
                value: organizationDetails.configurations.export_capabilities ? "Enabled" : "Disabled"
              }
            ]}
          />
        )}
      </div>
      <div className="flex justify-between mt-8">
          <DynamicButton
            variant="secondary"
            text="Back"
            onClick={onBack}
            disabled={isLoading}
          />
          <DynamicButton
            variant="primary"
            text={isLoading ? "Creating..." : "Confirm & Create"}
            onClick={handleConfirm}
            disabled={isLoading || !organizationDetails?.name || organizationDetails.name === 'Not provided'}
          />
        </div>
    </div>
  );
};

export default ReviewConfirm;
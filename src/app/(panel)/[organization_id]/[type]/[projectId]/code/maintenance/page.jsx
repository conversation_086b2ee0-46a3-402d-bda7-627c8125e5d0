"use client";
import React, { useState, useEffect, useContext, useRef } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams, usePathname } from 'next/navigation';
import { getPastMaintenanceTasks, updateTask, controlTask } from "@/utils/batchAPI";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { useWebSocket } from '@/components/Context/WebsocketContext';
import CodeGenerationModal from "@/app/modal/CodeGenerationModal";
import { GitBranch, Folder, ClipboardIcon, CheckIcon, Info } from 'lucide-react';
import TableComponent from "@/components/SimpleTable/ArchitectureTable";
import RepositoryDetailsModal from '@/components/Modal/RepositoryModal';
import { getkginfo } from '@/utils/gitAPI';
import { formatUTCToLocal, formatDateTime } from "@/utils/datetime";
import EditableCell from '@/components/SimpleTable/EditableCell';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { ConfirmationModal } from '@/components/CodeGenrationPanel/LoadingComponents';
import Cookies from "js-cookie";

const CodeMaintenanceInfo = () => {
  return (
    <div className="flex items-start gap-2 bg-orange-50 rounded-md p-2 border-l-2 border-orange-400 mb-2">
      <div className="text-orange-600 flex-shrink-0 mt-0.5">
        <Info size={14} />
      </div>
      <div>
        <h2 className="text-gray-800 font-weight-medium typography-body-sm">About Code Maintenance</h2>
        <i className="text-gray-600 mt-0.5 typography-caption leading-tight">
          Maintain and update existing code. Select repositories and perform maintenance tasks
        </i>
      </div>
    </div>
  );
};

const RepositoryCard = ({ repository, onSelect, isSelected }) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(repository.git_url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm p-3 flex flex-col gap-2 hover:shadow-md transition-all duration-200 ${
      isSelected
        ? 'border border-orange-400 bg-orange-50/30'
        : 'border border-gray-200 hover:border-orange-300'
    }`}>
      <div className="flex items-start gap-3">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={() => onSelect(repository)}
          className="w-4 h-4 flex-shrink-0 text-orange-600 rounded border-gray-300 focus:ring-orange-500 mt-0.5"
        />
        <div className="min-w-0 flex-1">
          <h3 className={`typography-body font-weight-semibold mb-1 leading-tight ${
            isSelected ? 'text-orange-700' : 'text-gray-800'
          }`}>
            {repository.repository_name}
          </h3>
          <p className="typography-body-sm mb-2 text-gray-500">{repository.service}</p>
          <span className="inline-block px-2 py-0.5 typography-body-sm rounded-full bg-orange-100 text-orange-700">
            {repository.service == "github" ? repository.repo_type : "files"}
          </span>
        </div>
      </div>

      <div className="border-t pt-3">
        <h4 className="text-gray-600 font-weight-medium typography-body-sm mb-2">Connected Repository</h4>
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 typography-body-sm bg-gray-50 rounded-md p-2">
              {repository.service !== "localFiles" ?
                <>
                  <span className="flex-1 truncate text-gray-600 hover:text-clip">
                    {repository.git_url.replace("https://","")}
                  </span>
                  <button
                    className={`flex-shrink-0 p-1 rounded-md transition-colors ${
                      copied
                        ? 'bg-green-50 text-green-600'
                        : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                    }`}
                    onClick={copyToClipboard}
                    aria-label="Copy to clipboard"
                  >
                    {copied ?
                      <CheckIcon size={12} className="transition-all duration-200" /> :
                      <ClipboardIcon size={12} className="transition-all duration-200" />
                    }
                  </button>
                </>
                :
                <>
                  <Folder size={14} className="text-gray-500 flex-shrink-0" />
                </>
              }
            </div>
            {repository.service !== "localFiles" &&
              <div className="flex items-center gap-2 typography-body-sm text-gray-500 mt-1 max-w-full">
                <GitBranch size={12} className="flex-shrink-0" />
                <span className="truncate" title={repository.branches[0]?.name || 'No branch'}>
                  {repository.branches[0]?.name || 'No branch'}
                </span>
              </div>
            }
          </div>
        </div>
      </div>
    </div>
  );
};

// Repository List Component
const RepositoryList = ({ repositories, isLoading, onSelect, selectedRepos }) => {
  if (isLoading) {
    return (
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-lg shadow p-4 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2 mb-6"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!repositories || repositories.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No repositories available</p>
        <p className="typography-body-sm text-gray-400 mt-2">Configure repositories to get started</p>
      </div>
    );
  }

  return (
    <div className="grid gap-2 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {repositories.map((repo) => (
        <RepositoryCard
          key={repo.repo_id}
          repository={repo}
          onSelect={onSelect}
          isSelected={selectedRepos.all_repositories || selectedRepos.repositories.includes(repo.repo_id)}
        />
      ))}
    </div>
  );
};

const CodeMaintenancePage = () => {
  const [pastTasks, setPastTasks] = useState([]);

  const [isLoading, setIsLoading] = useState(false);
  const [isInitiating, setIsInitiating] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [limit, setLimit] = useState(10);
  const [skip, setSkip] = useState(0);
  const [kgInfo, setKgInfo] = useState({ details: [] });
  const [showRepositoryModal, setShowRepositoryModal] = useState(false);
  const [selectedContainerId, setSelectedContainerId] = useState(null);
  const [selectedRepos, setSelectedRepos] = useState({
    all_repositories: false,
    repositories: []
  });
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [isRepoListLoading, setIsRepoListLoading] = useState(false);
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  const [sessionsTableData, setSessionsTableData] = useState([]);

  // Code Maintenance Handler state
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [activeSessions, setActiveSessions] = useState([]);
  const [showActiveSessionsModal, setShowActiveSessionsModal] = useState(false);
  const [isStoppingTasks, setIsStoppingTasks] = useState(false);
  const [logInfo, setLogInfo] = useState('');
  const [controller, setController] = useState(null);
  const [plannedTaskId, setPlannedTaskId] = useState(null);
  const hasGeneratedCode = useRef(false);

  const router = useRouter();
  const params = useParams();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { showAlert } = useContext(AlertContext);
  const { isVisible, setIsVisible, setIsCodeMaintenance, setCurrentIframeUrl } = useCodeGeneration();
  const { connectToSession, disconnectFromSession, getConnection, refreshRepos, setRefreshRepos } = useWebSocket();
  const projectId = params.projectId;

  const parseTimestamp = (timestampStr) => {
    try {
      return new Date(timestampStr);
    } catch (e) {
      throw new Error(`Invalid timestamp format: ${timestampStr}`);
    }
  };

  const calculateDuration = (startTime, endTime) => {
    try {
      const start = new Date(startTime);
      const end = new Date(endTime);

      // Calculate difference in milliseconds
      const diffMs = end.getTime() - start.getTime();

      // Convert to various units
      const seconds = Math.floor(diffMs / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);
      const weeks = Math.floor(days / 7);
      const years = Math.floor(days / 365.25);

      // Calculate remaining values
      const remainingWeeks = Math.floor((days % 365.25) / 7);
      const remainingDays = Math.floor(days % 7);
      const remainingHours = Math.floor(hours % 24);
      const remainingMinutes = Math.floor(minutes % 60);
      const remainingSeconds = Math.floor(seconds % 60);

      // Return appropriate unit based on duration with granular details
      if (years >= 1) {
        return `${years}y ${remainingWeeks}w`;
      } else if (weeks >= 1) {
        return `${weeks}w ${remainingDays}d`;
      } else if (days >= 1) {
        return `${days}d ${remainingHours}h`;
      } else if (hours >= 1) {
        return `${hours}h ${remainingMinutes}m`;
      } else if (minutes >= 1) {
        return `${minutes}m ${remainingSeconds}s`;
      } else {
        return `<1m`;
      }
    } catch (error) {

      return '0';
    }
  };

  const handleFieldUpdate = async (rowId, field, value) => {
    try {
      await updateTask(rowId, { [field]: value });
      const updatedData = pastTasks.map(item =>
        item._id === rowId ? { ...item, [field]: value } : item
      );
      setPastTasks(updatedData);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  const maintenanceHeaders = [
    {
      key: 'session_name',
      label: 'Session Name',
      render: (value, row) => (
        <EditableCell
          value={value || 'Untitled Session'}
          field="session_name"
          rowId={row.fullId}
          onUpdate={handleFieldUpdate}
          onError={(error) => showAlert("Failed to update session name", "error")}
          onSuccess={(message) => showAlert(message, "success")}
        />
      )
    },
    {
      key: 'start_time',
      label: 'Created at',
      render: (value) => value
    },
    {
      key: 'duration',
      label: 'Duration',
      render: (value) => value || '-'
    },
    {
      key: 'status',
      label: 'Status'
    }
  ];

  const activeSessionsHeaders = [
    {
      key: 'session_name',
      label: 'Session Name',
      render: (value, row) => (
        <span className="font-weight-medium">{value}</span>
      )
    },
    {
      key: 'job_id',
      label: 'Job ID',
      render: (value) => (
        <span className="typography-body-sm text-gray-600">{value.substring(0, 8)}...</span>
      )
    },
    {
      key: 'status',
      label: 'Status',
      render: (value) => (
        <span className={`px-2 py-1 typography-body-sm rounded-full ${value === 'RUNNING' ? 'bg-green-100 text-green-800' :
          value === 'SUBMITTED' ? 'bg-blue-100 text-blue-800' :
            'bg-gray-100 text-gray-800'
          }`}>
          {value}
        </span>
      )
    },
    {
      key: 'start_time',
      label: 'Start Time'
    },
    {
      key: 'action',
      label: 'Action',
      render: (_, row) => (
        <button
          className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded typography-body-sm"
          onClick={(e) => {
            e.stopPropagation();
            handleStopTask(row.job_id);
          }}
        >
          Stop
        </button>
      )
    }
  ];

  const handleStartMaintenance = async () => {
    // Check if any repositories are selected
    if (!selectedRepos.all_repositories && selectedRepos.repositories.length === 0) {
      showAlert("Please select at least one repository to start code maintenance session", "error");
      return;
    }

    // Check if no repositories are available
    if (kgInfo?.detail === "No repository is found") {
      showAlert("No repositories found. Please configure repositories first.", "error");
      return;
    }

    setIsInitiating(true);
    setLogInfo('');
    setIsGeneratingCode(true);
    hasGeneratedCode.current = false;
    setIsCompleted(false);
    setIsCodeMaintenance(true);
    sessionStorage.setItem("isCodeMaintenance", "true");

    let storedSelectedRepos = sessionStorage.getItem("selectedRepos");
    if (!storedSelectedRepos) {
      const filteredRepos = selectedRepos.all_repositories
        ? selectedRepos.all_repositories
        : selectedRepos.repositories.filter(repo => repo.repo_id);
      sessionStorage.setItem("selectedRepos", JSON.stringify(filteredRepos));
    }

    const abortController = new AbortController();
    setController(abortController);

    try {
      if (!hasGeneratedCode.current) {
        hasGeneratedCode.current = true;

        let url = `${process.env.NEXT_PUBLIC_API_URL}/batch/start_code_maintenance/${projectId}/`;

        fetchEventSource(
          url,
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${Cookies.get('idToken')}`,
              'Content-Type': 'application/json',
              'X-Tenant-Id': Cookies.get('tenant_id'),
            },
            body: JSON.stringify({
              request_timestamp: new Date().toISOString(),
              selectedrepos: selectedRepos.all_repositories
                ? { all_repositories: true }
                : { all_repositories: false, repositories: selectedRepos.repositories },
            }),
            signal: abortController.signal,
            openWhenHidden: true,
            onopen: (response) => {
              // showAlert("Starting code maintenance... ", "info");

              if (response.status !== 200) {
                showAlert("Something went wrong!", "error");
                abortController.abort();
                confirmClose();
              }
              return Promise.resolve();
            },
            onmessage: (event) => {
              try {
                const data = JSON.parse(event.data);

                if (data.task_id) {
                  const newSearchParams = new URLSearchParams(searchParams);
                  newSearchParams.set("task_id", data.task_id);
                  router.push(`${pathname}?${newSearchParams.toString()}`);
                }

                if (data.planned_job_id) {
                  setPlannedTaskId(data.planned_job_id);
                  connectToSession(data.planned_job_id);
                }

                if (data.message) {
                  setLogInfo(data.message);
                }

                if (data.end === true) {
                  if (data.task_id) {
                    // Handle successful completion
                    setIsCompleted(true);
                    setTimeout(() => {
                      if (data.iframe) {
                        setCurrentIframeUrl(data.iframe);
                      }
                      const newSearchParams = new URLSearchParams(searchParams);
                      newSearchParams.set("task_id", data.task_id);
                      router.push(`${pathname}?${newSearchParams.toString()}`);
                      setIsVisible(true);
                      setIsGeneratingCode(false);
                      setIsInitiating(false);
                      fetchPastTasks();
                    }, 3000);
                  } else if (data.error && data.error.includes("Maximum number of active code maintenance sessions reached")) {
                    try {
                      // Parse the active_sessions string into JSON
                      const sessions = JSON.parse(data.active_sessions);
                      setActiveSessions(sessions);
                      setShowActiveSessionsModal(true);
                      abortController.abort();
                      setIsGeneratingCode(false);
                      setIsInitiating(false);
                    } catch (parseError) {

                      showAlert(data.error, "error");
                      setIsGeneratingCode(false);
                      setIsInitiating(false);
                    }
                  } else {
                    showAlert(data.message || data.error || "Unable to start task", "error");
                    setIsGeneratingCode(false);
                    setIsInitiating(false);
                  }
                  abortController.abort();
                }
              } catch (error) {

                abortController.abort();
                showAlert("Error processing response", "error");
                setIsGeneratingCode(false);
                setIsInitiating(false);
              }
            },
            onerror: (error) => {

              showAlert("Error in code maintenance: " + error, "error");
              if (plannedTaskId) {
                disconnectFromSession(plannedTaskId);
              }
              abortController.abort();
              setIsGeneratingCode(false);
              setIsInitiating(false);
              hasGeneratedCode.current = false;
              return null;
            },
            onclose: () => {
              if (plannedTaskId) {
                disconnectFromSession(plannedTaskId);
              }
              showAlert("Connection closed", isCompleted ? "success" : "error");
              setIsGeneratingCode(false);
              setController(null);
              setIsInitiating(false);

              if (!isCompleted) {
                // Only reset visibility if not completed successfully
                hasGeneratedCode.current = false;
              }

              // Refresh task list
              fetchPastTasks();
            }
          }
        );
      }
    } catch (error) {

      if (plannedTaskId) {
        disconnectFromSession(plannedTaskId);
      }
      abortController.abort();
      showAlert("Failed to start code maintenance", "error");
      setIsGeneratingCode(false);
      setIsInitiating(false);
      hasGeneratedCode.current = false;
    }
  };

  const handleClose = () => setShowConfirmModal(true);

  const confirmClose = () => {
    if (controller) controller.abort();
    setShowConfirmModal(false);
    setIsGeneratingCode(false);
    setIsInitiating(false);
    hasGeneratedCode.current = false;
    setIsVisible(false);
    setCurrentIframeUrl(null);
    setPlannedTaskId(null);
  };

  const cancelClose = () => setShowConfirmModal(false);

  const handleStopTask = async (taskId) => {
    try {
      await controlTask(taskId, "stop", projectId);
      setActiveSessions(prevSessions => prevSessions.filter(session => session.job_id !== taskId));
      showAlert(`Successfully stopped task ${taskId}`, "success");
      setPlannedTaskId(null);
      if (activeSessions.length <= 1) {
        setShowActiveSessionsModal(false);
        setIsInitiating(false);
      }
    } catch (error) {
      showAlert(`Failed to stop task: ${error.message}`, "error");
    }
  };

  const handleStopAllTasks = async () => {
    try {
      setIsStoppingTasks(true);
      await Promise.all(
        activeSessions.map(session =>
          controlTask(session.job_id, "stop", projectId)
            .catch(error => { })
        )
      );
      showAlert("Successfully stopped all tasks", "success");
      setShowActiveSessionsModal(false);
      setIsInitiating(false);
    } catch (error) {
      showAlert(`Failed to stop all tasks: ${error.message}`, "error");
    } finally {
      setIsStoppingTasks(false);
    }
  };

  const fetchKgInfo = async () => {
    try {
      setIsRepoListLoading(true);
      const data = await getkginfo(projectId, undefined, true);
      setKgInfo(data);
      
      // Update selected repositories if needed - only if there were previous selections
      if (selectedRepos.all_repositories || selectedRepos.repositories.length > 0) {
        // If all repositories were selected, maintain that state
        if (selectedRepos.all_repositories) {
          setSelectedRepos({
            all_repositories: true,
            repositories: []
          });
        } else {
          // Filter out any repository IDs that no longer exist
          const validRepoIds = data.details?.map(repo => repo.repo_id) || [];
          const filteredRepos = selectedRepos.repositories.filter(repoId =>
            validRepoIds.includes(repoId)
          );

          setSelectedRepos({
            all_repositories: false,
            repositories: filteredRepos
          });
        }
      }
    } catch (error) {
      showAlert("Failed to fetch repository information", "error");
    } finally {
      setIsRepoListLoading(false);
    }
  };

  const handleRepoSelect = (repo) => {
    setSelectedRepos(prev => {
      if (prev.all_repositories) {
        // When deselecting from "all selected" state
        const allRepoIds = kgInfo.details.map(r => r.repo_id);
        return {
          all_repositories: false,
          repositories: allRepoIds.filter(id => id !== repo.repo_id)
        };
      } else {
        // Normal toggle behavior
        const newRepos = prev.repositories.includes(repo.repo_id)
          ? prev.repositories.filter(id => id !== repo.repo_id)
          : [...prev.repositories, repo.repo_id];

        const allSelected = newRepos.length === kgInfo.details.length;
        return {
          all_repositories: allSelected,
          repositories: allSelected ? [] : newRepos
        };
      }
    });
    sessionStorage.setItem("selectedRepos", JSON.stringify(selectedRepos));
  };

  const handleSelectAll = () => {
    setSelectedRepos(prev => ({
      all_repositories: !prev.all_repositories,
      repositories: []  // Clear repositories array when toggling all
    }));
    setIsAllSelected(!selectedRepos.all_repositories);
    sessionStorage.setItem("selectedRepos", JSON.stringify(selectedRepos));
  };

  const handleConfigureRepository = (containerId) => {
    setSelectedContainerId(containerId);
    setShowRepositoryModal(true);
  };

  const handleRepositoryModalClose = () => {
    setShowRepositoryModal(false);
    // Refresh containers and repositories after modal is closed
    fetchKgInfo();
    // Also trigger refresh for other components
    setRefreshRepos(true);
  };

  const handleSetPastTasks = (tasks, skip) => {
    let newTasks = [];
    newTasks.push(...tasks);
    setPastTasks(newTasks);
  };

  const format_task = (data) => {
    const tableData = data.map(task => {
      const startTime = task.start_time;
      const endTime = task.messages && task.messages.length > 0
        ? task.messages[task.messages.length - 1].timestamp
        : (task.status === 'RUNNING' ? new Date().toISOString() : startTime);

      return {
        session_name: task.session_name || 'Untitled Session',
        description: task.description || 'No description',
        messages_length: Array.isArray(task.messages) ? task.messages.length : 0,
        status: task.status,
        fullId: task._id,
        start_time: formatUTCToLocal(task.start_time),
        duration: `${calculateDuration(startTime, endTime)}`
      };
    });
    return tableData;
  };

  const fetchPastTasks = async (currentSkip = 0, currentLimit = limit) => {
    setIsHistoryLoading(true);
    try {
      const data = await getPastMaintenanceTasks(projectId, currentLimit, currentSkip);
      handleSetPastTasks(data.tasks, currentSkip);
      setTotalCount(data.total_count);
      setSkip(currentSkip);
      setLimit(currentLimit);
    } catch (error) {
      showAlert("Failed to fetch past maintenance tasks", "error");
    } finally {
      setIsHistoryLoading(false);
    }
  };

  const handlePageChange = async (newPage) => {
    const newSkip = (newPage - 1) * limit;
    await fetchPastTasks(newSkip, limit);
  };

  const handleLimitChange = async (newLimit) => {
    await fetchPastTasks(0, newLimit);
  };

  const handleRowClick = (row) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("task_id", row.fullId);
    router.push(`${pathname}?${newSearchParams.toString()}`);
    setIsVisible(true);
  };

  const handleSort = (column, direction) => {
    const sortedData = [...sessionsTableData].sort((a, b) => {
      if (column === 'start_time') {
        const dateA = new Date(a.start_time);
        const dateB = new Date(b.start_time);
        return direction === 'asc' ? dateA - dateB : dateB - dateA;
      }
      return 0;
    });
    setSessionsTableData(sortedData);
  };

  // Check if all repos are selected
  useEffect(() => {
    setIsAllSelected(
      selectedRepos.repositories &&
      kgInfo?.details &&
      selectedRepos.repositories.length === kgInfo.details.length &&
      kgInfo.details.length > 0
    );
  }, [selectedRepos.repositories, kgInfo?.details]);

  // Fetch repository information
  useEffect(() => {
    fetchKgInfo();
  }, [projectId]);

  // Listen for repository changes from other components
  useEffect(() => {
    if (refreshRepos) {
      fetchKgInfo();
      setRefreshRepos(false);
    }
  }, [refreshRepos]);

  // Refresh repository data when component becomes visible again
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        fetchKgInfo();
      }
    };

    const handleFocus = () => {
      fetchKgInfo();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  // Format active sessions for table display
  useEffect(() => {
    if (activeSessions.length > 0) {
      // Transform session data for the table component
      const formattedSessions = activeSessions.map(session => ({
        _id: session._id || '',
        job_id: session.job_id || '',
        fullId: session.job_id || '',
        session_name: session.session_name || `Session ${session.job_id.substring(0, 8)}`,
        status: session.status || 'UNKNOWN',
        start_time: formatDateTime(session.start_time, true),
        ip: session.ip || ''
      }));
      setSessionsTableData(formattedSessions);
    }
  }, [activeSessions]);

  // Fetch past tasks
  useEffect(() => {
    fetchPastTasks();
  }, [projectId]);

  // Check for task_id in URL
  useEffect(() => {
    const taskId = searchParams.get('task_id');
    if (taskId) {
      setIsVisible(true);
    } else {
      fetchPastTasks();
    }
  }, [searchParams]);

  // Handle WebSocket connections for planned tasks
  useEffect(() => {
    if (plannedTaskId) {
      const connection = getConnection(plannedTaskId);
      if (connection && connection.readyState === WebSocket.OPEN) {
        connection.send(JSON.stringify({
          type: 'client',
          task_id: plannedTaskId
        }));

        connection.onmessage = (event) => {
          setPlannedTaskId(null);
          const newSearchParams = new URLSearchParams(searchParams);
          newSearchParams.set("task_id", plannedTaskId);
          router.push(`${pathname}?${newSearchParams.toString()}`);
          setIsVisible(true);
        };
      }
    }
  }, [plannedTaskId]);

  // Clean up controller on unmount
  useEffect(() => {
    return () => {
      if (controller) {
        controller.abort();
      }
      if (plannedTaskId) {
        disconnectFromSession(plannedTaskId);
      }
    };
  }, []);

  // Listen for refresh event from parent
  useEffect(() => {
    const handleRefresh = () => {
      fetchKgInfo();
    };

    window.addEventListener('refreshMaintenance', handleRefresh);
    return () => window.removeEventListener('refreshMaintenance', handleRefresh);
  }, []);

  // Sync parent header buttons with maintenance page state
  useEffect(() => {
    const selectAllBtn = document.getElementById('maintenance-select-all-btn');
    const startSessionBtn = document.getElementById('maintenance-start-session-btn');

    if (selectAllBtn) {
      // Update select all button
      const checkbox = selectAllBtn.querySelector('div > div');
      const text = selectAllBtn.querySelector('span');

      if (checkbox && text) {
        if (selectedRepos.all_repositories) {
          checkbox.classList.add('bg-orange-500', 'border-orange-500');
          checkbox.classList.remove('border-gray-400');
          checkbox.innerHTML = '<svg width="8" height="8" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8.5 3L4 7.5L1.5 5" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" /></svg>';
          text.textContent = 'Deselect All';
        } else {
          checkbox.classList.remove('bg-orange-500', 'border-orange-500');
          checkbox.classList.add('border-gray-400');
          checkbox.innerHTML = '';
          text.textContent = 'Select All';
        }
      }

      // Add click handler
      selectAllBtn.onclick = handleSelectAll;
    }

    if (startSessionBtn) {
      // Store original content if not already stored
      if (!startSessionBtn.dataset.originalContent) {
        startSessionBtn.dataset.originalContent = startSessionBtn.innerHTML;
      }

      // Update start session button state
      const isDisabled = isStartButtonDisabled();
      startSessionBtn.disabled = isDisabled || isInitiating;

      // Update button content and styling based on state
      if (isInitiating) {
        // Show loading state
        startSessionBtn.innerHTML = `
          <svg class="animate-spin -ml-1 mr-2 h-3.5 w-3.5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Starting...</span>
        `;
        startSessionBtn.classList.add('opacity-75', 'cursor-not-allowed');
        startSessionBtn.classList.remove('hover:bg-orange-600');
      } else {
        // Restore original button content (now just text without Play icon)
        startSessionBtn.innerHTML = startSessionBtn.dataset.originalContent || '<span>Start Session</span>';
        
        if (isDisabled) {
          startSessionBtn.classList.add('opacity-50', 'cursor-not-allowed');
          startSessionBtn.classList.remove('hover:bg-orange-600');
        } else {
          startSessionBtn.classList.remove('opacity-50', 'cursor-not-allowed', 'opacity-75');
          startSessionBtn.classList.add('hover:bg-orange-600');
        }
      }

      // Add click handler only if not initiating
      if (!isInitiating) {
        startSessionBtn.onclick = handleStartMaintenance;
      } else {
        startSessionBtn.onclick = null;
      }
    }
  }, [selectedRepos, isInitiating, kgInfo]);

  // Check if start button should be disabled
  const isStartButtonDisabled = () => {
    if (kgInfo?.detail == "No repository is found")

      return isInitiating || (!selectedRepos.all_repositories && selectedRepos.repositories.length === 0) || kgInfo?.detail == "No repository is found";
  };

  const tableData = format_task(pastTasks);

  return (
    <div className="flex flex-col">
      {/* Content */}
      <div className="bg-white">
        <div className="px-1 py-2">


          {/* Info Section */}
          <CodeMaintenanceInfo />

          {/* Repositories List */}
          <div className="pt-2">
            <RepositoryList
              repositories={kgInfo?.details || []}
              isLoading={isRepoListLoading}
              onSelect={handleRepoSelect}
              selectedRepos={selectedRepos}
            />
          </div>
        </div>
      </div>


      {/* Modals */}
      <div className='p-4 flex-shrink-0'>
        {isVisible && <CodeGenerationModal />}
        {showRepositoryModal && (
          <RepositoryDetailsModal
            open={showRepositoryModal}
            onClose={handleRepositoryModalClose}
            projectId={projectId}
            containerId={selectedContainerId}
          />
        )}
      </div>

      {/* Loading and Confirmation Modals */}
      {/* {isGeneratingCode && (
        <FullScreenLoader
          logginfo={logInfo}
          onClose={handleClose}
          isCompleted={isCompleted}
        />
      )} */}
      {showConfirmModal && (
        <ConfirmationModal
          onConfirm={confirmClose}
          onCancel={cancelClose}
        />
      )}
      {showActiveSessionsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="p-6">
              <h2 className="typography-heading-4 text-black font-weight-semibold mb-4">Active Maintenance Sessions</h2>
              <p className="mb-4 text-red-500">
                Maximum number of active code maintenance sessions reached (limit: 3).
                Please stop some sessions and try again.
              </p>

              <div className="overflow-auto max-h-[50vh]">
                <TableComponent
                  data={sessionsTableData}
                  headers={activeSessionsHeaders}
                  sortableColumns={{ start_time: true, status: true }}
                  onSort={handleSort}
                  defaultSort={{ column: 'start_time', direction: 'desc' }}
                  emptyMessage="No active sessions found"
                />
              </div>

              <div className="mt-6 flex justify-end gap-4">
                <button
                  className="bg-gray-300 text-black hover:bg-gray-400 dark:bg-gray-700 dark:hover:bg-gray-600 px-4 py-2 rounded"
                  onClick={() => {
                    setShowActiveSessionsModal(false);
                    setIsInitiating(false);
                    hasGeneratedCode.current = false;
                  }}
                >
                  Close
                </button>
                <button
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded flex items-center"
                  onClick={handleStopAllTasks}
                  disabled={isStoppingTasks}
                >
                  {isStoppingTasks ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </>
                  ) : "Stop All Sessions"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};


export default CodeMaintenancePage;
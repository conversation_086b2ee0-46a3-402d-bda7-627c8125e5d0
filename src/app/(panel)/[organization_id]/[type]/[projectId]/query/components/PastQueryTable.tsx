import React, { useState, useMemo ,useCallback,useContext } from 'react';
import { ChevronDown, ChevronUp, CodeXml,Eye,Trash2} from 'lucide-react';
import Pagination from '@/components/UIComponents/Paginations/Pagination';
import { DynamicButton } from '@/components/UIComponents/Buttons/DynamicButton';
import DeleteProjectModal from "@/components/Modal/DeleteProjectModal";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { deleteQueryDiscussion } from '@/utils/api';
import EmptyState from './EmptyState';

interface HeaderProps {
  key: string;
  label: string;
  actionLabel?: string;
}

interface SortConfig {
  key: string | null;
  direction: 'ascending' | 'descending';
}

interface PastQueryTableProps {
  headers: HeaderProps[];
  data: Array<{ [key: string]: any }>;
  onRowClick: (data: {[key: string]: any}) => void;
  sortableColumns?: { [key: string]: boolean };
  onActionClick?: (id: string | number) => void;
  title?: string;
}

const PastQueryTable: React.FC<PastQueryTableProps> = ({
  headers,
  data: initialData = [],
  onRowClick,
  sortableColumns = {},
  onActionClick,
  title,
}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: 'ascending' });
  const [data, setData] = useState(initialData);
  const [dropdownOpen, setDropdownOpen] = useState<{ [key: string]: boolean }>({});
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(20);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [discussionId, setDiscussionId] = useState<any>()
  const { showAlert } = useContext(AlertContext);


  const handleDelete =  useCallback(async(id: string | number) => {
    setIsDeleting(true);
    try {
        const response = await deleteQueryDiscussion(id);
        if (response) {
          showAlert("Discussion deleted successfully", "success");
          const updatedData = data.filter(item => item.id !== id);
          setData(updatedData);

          // Reset dropdown state
          const newDropdownState = { ...dropdownOpen };
          delete newDropdownState[id];
          setDropdownOpen(newDropdownState);

          // If current page becomes empty, go to previous page
          const newPageCount = Math.ceil(updatedData.length / pageSize);
          if (currentPage > newPageCount) {
            setCurrentPage(Math.max(1, newPageCount));
          }
        }
    }catch (error) {
      
      showAlert("Failed to delete the Discussion!", "danger");
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
    }
  }, [data, dropdownOpen, pageSize, currentPage]);
  

  const confirmAndDelete = (id: string | number) => {
    setIsDeleteModalOpen(true);
    setDiscussionId(id)
  };

  const cancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  const proceedWithDelete = async () => {
    await handleDelete(discussionId);
  };


  const sortedData = useMemo(() => {
    if (!sortConfig.key) return data;
    return [...data].sort((a, b) => {
      const aVal = a[sortConfig.key as string];
      const bVal = b[sortConfig.key as string];
      if (aVal < bVal) return sortConfig.direction === 'ascending' ? -1 : 1;
      if (aVal > bVal) return sortConfig.direction === 'ascending' ? 1 : -1;
      return 0;
    });
  }, [data, sortConfig]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return sortedData.slice(startIndex, startIndex + pageSize);
  }, [sortedData, currentPage, pageSize]);

  const totalItems = sortedData.length;
  const pageCount = Math.ceil(totalItems / pageSize);

  const TableHeader: React.FC<{ header: HeaderProps }> = ({ header }) => (
    <th
      key={header.key}
      onClick={() => sortableColumns[header.key] && requestSort(header.key)}
      className={`px-6 py-2.5 typography-heading-6 text-[#687182] uppercase ${['id', 'action', 'status', 'type'].includes(header.key) ? 'text-center' : ''} ${sortableColumns[header.key] ? 'cursor-pointer hover:bg-gray-50' : ''
        }`}
      style={{ minWidth: header.key.toLowerCase() === 'title' ? '200px' : header.key.toLowerCase() === 'id' ? '180px': header.key.toLowerCase() === 'timestamp'? '170px' :'auto' }}
    >
      <div className={`flex items-center space-x-1 ${['id', 'action', 'status', 'type'].includes(header.key) ? 'justify-center' : ''}`}>
        <span>{header.label}</span>
        {sortableColumns[header.key] && (
          <div className="flex flex-col">
            <ChevronUp
              className={`h-3 w-3 ${sortConfig.key === header.key && sortConfig.direction === 'ascending'
                ? 'text-blue-600'
                : 'text-[#A1A9B8]'
                }`}
            />
            <ChevronDown
              className={`h-3 w-3 ${sortConfig.key === header.key && sortConfig.direction === 'descending'
                ? 'text-blue-600'
                : 'text-[#A1A9B8]'
                }`}
            />
          </div>
        )}
      </div>
    </th>
  );

  const requestSort = (key: string) => {
    if (!sortableColumns[key]) return;
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'ascending' ? 'descending' : 'ascending'
    }));
  };


  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };
  return(
    <>
      {data.length > 0 ?
      (
        <div className="w-full bg-white border border-[#E9EDF5] rounded-lg shadow">
          {
            title && (
              <div className="bg-gray-100 px-4 py-2 font-weight-medium text-gray-700">
                {title}
              </div>
            )
          }
          <div className="overflow-x-auto w-full">
            <table className="w-full border-collapse divide-y divide-[#E9EDF5]">
              <thead className="bg-[#F7F9FCCC]">
                <tr>
                  {headers.map((header) => (
                    <TableHeader key={header.key} header={header} />
                  ))}
                  {/* <th className="px-6 py-2.5 typography-heading-6 text-[#687182] uppercase"></th> */}
                  <th className="px-6 py-2.5 typography-heading-6 text-[#687182] uppercase text-center">
                  Actions
                </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-[#E9EDF5]">
                {paginatedData.map((row, index) => (
                  <tr
                    key={row.id || index}
                    // onClick={() => onRowClick(row)}
                    className="hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    {headers.map((header) => (
                      <td
                        key={header.key}
                        className={`px-6 py-2.5 ${['id', 'action', 'status', 'type'].includes(header.key) ? 'text-center' : ''}`}
                        style={{
                          minWidth: header.key.toLowerCase() === 'title' ? '230px'
                          : header.key.toLowerCase() === 'description' ? '200px' : 'auto'
                        }}
                      >
                        {header.key === 'action' ? (
                          <DynamicButton
                            type="submit"
                            variant="primaryOutline"
                            icon={CodeXml}
                            onClick={(e) => {
                              e.stopPropagation();
                              onActionClick?.(row.id);
                            }}
                            text={header.actionLabel || 'Action'}
                          />
                        ) : header.key === 'type' ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full typography-caption font-weight-medium bg-gray-100 text-[#2A3439]">
                            {row[header.key]}
                          </span>
                        ) :
                          (
                            <div className={`typography-body-sm text-[#2A3439] custom-table-text-ellipsis ${header.key.toLowerCase() === "title" ? "hover:text-blue-600" : ""
                              }`}
                              title={header.key.toLowerCase() === "description" ? row[header.key] : undefined}>
                              {row[header.key]}
                            </div>
                          )}
                      </td>
                    ))}
                    <td className="text-center px-4 py-2 flex justify-center space-x-4">
                    <button
                      onClick={() => onRowClick(row)}
                      className="text-gray-500 hover:text-blue-600 transition-colors"
                      title="View"
                    >
                      <Eye size={20} />
                    </button>
                    <button
                      onClick={() => confirmAndDelete(row.id)}
                      className="text-gray-500 hover:text-red-600 transition-colors"
                      title="Delete"
                    >
                      <Trash2 size={20} />
                    </button>
                  </td>
                      {/* <td className="text-right px-4 py-2">
                        <div className="relative">
                          <button
                            className="text-gray-500 hover:text-gray-700"
                            onClick={(e) => {
                              e.stopPropagation();
                              setDropdownOpen((prev) => ({ ...prev, [row.id]: !prev[row.id] }));
                            }}
                          >
                            <MoreVertical className="w-5 h-5" />
                          </button>
                          {dropdownOpen[row.id] && (
                            <div className={`absolute right-0 w-32 bg-white border rounded shadow-lg z-10 bottom-full mb-1`}>
                              <button
                                className="block w-full text-left px-4 py-2 typography-body-sm text-red-700 hover:bg-red-50"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  confirmAndDelete(row.id);
                                }}
                              >
                                Delete
                              </button>
                            </div>
                          )}
                        </div>
                      </td> */}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <Pagination
            currentPage={currentPage}
            pageCount={pageCount}
            pageSize={pageSize}
            totalItems={totalItems}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
          {isDeleteModalOpen && (
            <DeleteProjectModal
              isOpen={isDeleteModalOpen}
              onClose={cancelDelete}
              onDelete={proceedWithDelete}
              isDeleting={isDeleting}
              type="discussion"
            />
          )}
        </div>
      ): <div className="text-center flex justify-center  items-center"><EmptyState /></div>
    }
    </>
  )
  
  return(
    <div className="text-center flex justify-center  items-center"><EmptyState /></div>
  )
};

export default PastQueryTable;
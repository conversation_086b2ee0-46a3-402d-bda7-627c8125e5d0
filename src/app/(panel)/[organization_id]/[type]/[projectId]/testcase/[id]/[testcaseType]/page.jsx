"use client";
import React, { useState, useEffect, useContext } from "react";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";
import { fetchNodeById, updateNodeByPriority, fetchChildNodes } from "@/utils/api";
import { CardLoadingSkeleton } from "@/components/UIComponents/Loaders/LoaderGroup";
import ErrorView from "@/components/Modal/ErrorViewModal";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import CustomDropdown from '@/components/UIComponents/Dropdowns/CustomDropdown';
import { BookOpen, ArrowLeft, ArrowRight, X } from "lucide-react";
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { getTestCaseRootById } from "@/utils/testcaseApi";
import { buildProjectUrl } from "@/utils/navigationHelpers";

const TestCaseDetails = () => {
  const [testCase, setTestCase] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [uniqueIds, setUniqueIds] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [testCaseRoot, setTestCaseRoot] = useState(null);
  const [testcaseType, setTestcaseType] = useState(null);

  const params = useParams();
  const { showAlert } = useContext(AlertContext) || { showAlert: () => { } };

  const testCaseId = params.id;
  const projectId = params.projectId;
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    setTestcaseType(params.testcaseType);
  }, [params.testcaseType]);

  useEffect(() => {
    const fetchTestCaseData = async () => {
      if (testCaseId && params.testcaseType) {
        try {
          // Get test case data by ID
          const testCaseData = await fetchNodeById(testCaseId, params.testcaseType);
          setTestCase(testCaseData);

          // Get test case root data
          const rootData = await getTestCaseRootById(testCaseId);
          setTestCaseRoot(rootData);

          // Get all test cases to enable navigation between them
          const testCaseRootId = await fetchChildNodes(projectId, 'Project', 'TestCaseRoot');
          if (testCaseRootId && testCaseRootId.length > 0) {
            const testCases = await fetchChildNodes(testCaseRootId[0].id, 'TestCaseRoot', testcaseType);
            const ids = testCases.map(item => item.id);
            setUniqueIds(ids);
            setCurrentIndex(ids.findIndex(id => id === testCaseId));
          }
        } catch (err) {

          setError(err);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchTestCaseData();
  }, [testCaseId, projectId]);

  // Handle property updates
  const handlePropertyUpdate = async (key, value) => {
    try {
      const response = await updateNodeByPriority(testCaseId, key, value);

      if (response === "success") {
        // Update local state
        setTestCase(prev => ({
          ...prev,
          properties: {
            ...prev.properties,
            [key]: value
          }
        }));
        showAlert("Test case updated successfully", "success");
      } else {
        throw new Error('Update failed');
      }
    } catch (error) {

      showAlert("Failed to update test case", "error");
    }
  };

  const handleViewPastDiscussion = async () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", testCaseId);
    newSearchParams.set("discussion_type", `TestCase`);
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const menuOptions = [
    {
      label: "History",
      icon: BookOpen,
      onClick: handleViewPastDiscussion,
      tooltip: TOOLTIP_CONTENT.WorkItems.viewPastDiscussion,
    },
  ];

  const handleUpdateTestCase = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", testCaseRoot?.id || testCaseId);
    newSearchParams.set("node_type", testCaseRoot?.properties?.Type || "TestCase");
    newSearchParams.set("discussion_type", "testcase_generation");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Test Case"
        message="There was a problem loading the test case details. Please try again."
        onRetry={() => window.location.reload()}
        panelType='main'
      />
    );
  }

  return (
    <div className="w-full h-full flex flex-col">
      {/* Fixed Header */}
      <div className="flex items-center justify-between px-4 py-2 border-b shrink-0">
        <div className="flex items-center space-x-2">
          <IconButton
            icon={<X strokeWidth={3} className="h-4 w-4 text-gray-600" />}
            tooltip={testCase?.properties?.Type === "UserStory" ? "Go to requirements" : "Go to test case table"}
            onClick={() => router.push(
              testCase?.properties?.Type === "UserStory"
                ? buildProjectUrl(projectId, 'requirements')
                : buildProjectUrl(projectId, 'testcase')
            )}
            className="hover:bg-gray-100"
            variant="small"
          />
          <div className="w-px h-4 bg-gray-100" />
          <IconButton
            icon={<ArrowLeft strokeWidth={3} className="h-4 w-4 text-gray-600" />}
            tooltip={testCase?.properties?.Type === "UserStory" ? "Go to previous user story" : "Go to previous test case"}
            onClick={() => {
              if (currentIndex > 0) {
                router.push(buildProjectUrl(projectId, `testcase/${uniqueIds[currentIndex - 1]}/functional`));
              }
            }}
            className="hover:bg-gray-100"
            variant="small"
          />
          <IconButton
            icon={<ArrowRight strokeWidth={3} className="h-4 w-4 text-gray-600" />}
            tooltip={testCase?.properties?.Type === "UserStory" ? "Go to next user story" : "Go to next test case"}
            onClick={() => {
              if (currentIndex >= 0 && currentIndex < uniqueIds.length - 1) {
                router.push(buildProjectUrl(projectId, `testcase/${uniqueIds[currentIndex + 1]}/functional`));
              }
            }}
            className="hover:bg-gray-100"
            variant="small"
          />
        </div>

        <div className="flex items-center space-x-2">
          <DynamicButton
            variant="primary"
            text={testCase?.properties?.Type === "UserStory" ? "Update User Story" : "Update Test Case"}
            onClick={handleUpdateTestCase}
          />
          <CustomDropdown options={menuOptions} align="right" />
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-auto">
        {loading ? (
          <div className="p-4">
            <CardLoadingSkeleton />
          </div>
        ) : (
          <div className="p-4 pb-32">
            {testCase ? (
              <div className="space-y-6">
                {/* Test Case Root Information */}
                {testCaseRoot && (
                  <div className="bg-blue-50 rounded-lg border border-blue-100 shadow-sm p-4 mb-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <h2 className="typography-body-sm font-weight-medium text-blue-800">Test Case Root</h2>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="typography-caption font-weight-medium text-blue-700">
                            ID: {testCaseRoot?.id}
                          </span>
                          {testCaseRoot?.labels && testCaseRoot.labels.length > 0 && (
                            <span className="typography-caption font-weight-medium text-blue-700 bg-blue-100 px-2 py-0.5 rounded">
                              {testCaseRoot?.labels.join(', ')}
                            </span>
                          )}
                        </div>
                      </div>
                      {testCaseRoot?.properties?.Type && (
                        <span className="px-3 py-1 typography-caption font-weight-bold rounded-full bg-blue-200 text-blue-800">
                          {testCaseRoot?.properties?.Type}
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* Properties Section - Top Section with Key Information */}
                <div className="bg-white rounded-lg border shadow-sm p-4">
                  <div className="flex flex-col space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex flex-col">
                        <div className="flex items-center space-x-2">
                          <span className="px-3 py-1 typography-caption font-weight-medium rounded-full bg-blue-100 text-blue-800">
                            {testCase?.properties?.Type || "Test Case"}
                          </span>
                          <span className="typography-body-sm text-gray-500">
                            {testCase?.labels?.length > 0 ? testCase.labels.join(', ') : "No labels"}
                          </span>
                        </div>
                        <h1 className="typography-body-lg font-weight-semibold text-gray-800 mt-2">{testCase?.properties?.Title || "Untitled Test Case"}</h1>
                      </div>
                      <div className="flex flex-col items-end space-y-2">
                        {testCase?.properties?.Category && (
                          <span className="px-3 py-1 typography-caption font-weight-medium rounded-full bg-gray-100 text-gray-800">
                            Category: {testCase?.properties?.Category}
                          </span>
                        )}
                        {Array.isArray(testCase?.properties?.Tags) && (
                          <div className="flex flex-wrap gap-1 justify-end">
                            {testCase.properties.Tags.map((tag, index) => (
                              <span key={index} className="px-2 py-0.5 bg-gray-100 rounded typography-caption">
                                {tag.trim()}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mt-2">
                      <div className="flex items-center">
                        <span className="typography-body-sm font-weight-medium text-gray-500 w-24">Type</span>
                        <span className="typography-body-sm text-gray-700">{testCase?.properties?.Type || "Unknown"}</span>
                      </div>

                      <div className="flex items-center">
                        <span className="typography-body-sm font-weight-medium text-gray-500 w-24">Status</span>
                        <span className="typography-body-sm text-gray-700">{testCase?.properties?.Status || "Not started"}</span>
                      </div>

                      <div className="flex items-center">
                        <span className="typography-body-sm font-weight-medium text-gray-500 w-24">Category</span>
                        <span className="typography-body-sm text-gray-700">
                          {testCase?.properties?.Category || "Not specified"}
                        </span>
                      </div>

                      <div className="flex items-center">
                        <span className="typography-body-sm font-weight-medium text-gray-500 w-24">Tags</span>
                        <div className="typography-body-sm text-gray-700">
                          {Array.isArray(testCase?.properties?.Tags) ? (
                            <div className="flex flex-wrap gap-1">
                              {testCase.properties.Tags.map((tag, index) => (
                                <span key={index} className="px-2 py-0.5 bg-gray-100 rounded typography-caption">
                                  {tag.trim()}
                                </span>
                              ))}
                            </div>
                          ) : (
                            "None"
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Description Section */}
                <div className="bg-white rounded-lg border shadow-sm p-4">
                  <h2 className="typography-body-lg font-weight-medium text-gray-800 mb-4">Description</h2>
                  <p className="typography-body-sm text-gray-700 whitespace-pre-wrap">
                    {testCase?.properties?.Description || "No description available"}
                  </p>
                </div>

                {/* Test Case Details Section */}
                {testCase?.properties?.Type === "FunctionalTestCase" && (
                  <div className="bg-white rounded-lg border shadow-sm p-4">
                    <h2 className="typography-body-lg font-weight-medium text-gray-800 mb-4">Test Steps</h2>
                    <div className="space-y-6">
                      <div className="mb-4">
                        <h3 className="typography-body-sm font-weight-semibold text-gray-700 mb-1">Pre-Conditions</h3>
                        <div className="p-3 bg-gray-50 rounded-md typography-body-sm text-gray-600 whitespace-pre-wrap">
                          {testCase?.properties?.PreConditions || "None specified"}
                        </div>
                      </div>

                      <div className="mb-4">
                        <h3 className="typography-body-sm font-weight-semibold text-gray-700 mb-1">Steps</h3>
                        <div className="p-3 bg-gray-50 rounded-md typography-body-sm text-gray-600 whitespace-pre-wrap">
                          {testCase?.properties?.Steps || "None specified"}
                        </div>
                      </div>

                      <div className="mb-4">
                        <h3 className="typography-body-sm font-weight-semibold text-gray-700 mb-1">Expected Result</h3>
                        <div className="p-3 bg-gray-50 rounded-md typography-body-sm text-gray-600 whitespace-pre-wrap">
                          {testCase?.properties?.ExpectedResult || "None specified"}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Non-Functional Test Case Details */}
                {testCase?.properties?.Type === "NonFunctionalTestCase" && (
                  <div className="bg-white rounded-lg border shadow-sm p-4">
                    <h2 className="typography-body-lg font-weight-medium text-gray-800 mb-4">Test Details</h2>
                    <div className="space-y-6">
                      <div className="mb-4">
                        <h3 className="typography-body-sm font-weight-semibold text-gray-700 mb-1">Test Procedure</h3>
                        <div className="p-3 bg-gray-50 rounded-md typography-body-sm text-gray-600 whitespace-pre-wrap">
                          {testCase?.properties?.TestProcedure || "None specified"}
                        </div>
                      </div>

                      <div className="mb-4">
                        <h3 className="typography-body-sm font-weight-semibold text-gray-700 mb-1">Measurement Metrics</h3>
                        <div className="p-3 bg-gray-50 rounded-md typography-body-sm text-gray-600 whitespace-pre-wrap">
                          {testCase?.properties?.MeasurementMetrics || "None specified"}
                        </div>
                      </div>

                      <div className="mb-4">
                        <h3 className="typography-body-sm font-weight-semibold text-gray-700 mb-1">Test Environment</h3>
                        <div className="p-3 bg-gray-50 rounded-md typography-body-sm text-gray-600 whitespace-pre-wrap">
                          {testCase?.properties?.TestEnvironment || "None specified"}
                        </div>
                      </div>

                      <div className="mb-4">
                        <h3 className="typography-body-sm font-weight-semibold text-gray-700 mb-1">Acceptance Criteria</h3>
                        <div className="p-3 bg-gray-50 rounded-md typography-body-sm text-gray-600 whitespace-pre-wrap">
                          {testCase?.properties?.AcceptanceCriteria || "None specified"}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* UI metadata based fields */}
                {testCase?.ui_metadata && (
                  <div className="bg-white rounded-lg border shadow-sm p-4">
                    <h2 className="typography-body-lg font-weight-medium text-gray-800 mb-4">
                      {testCase?.properties?.Type === "UserStory" ? "User Story Details" : "Additional Details"}
                    </h2>
                    <div className="space-y-6">
                      <PropertiesRenderer
                        properties={testCase?.properties || {}}
                        metadata={testCase?.ui_metadata || {}}
                        to_skip={
                          testCase?.properties?.Type === "UserStory"
                            ? ["Title", "Type", "Category", "Tags", "Description", "Status"]
                            : ["Title", "Type", "Category", "Tags", "Description", "PreConditions", "Steps", "ExpectedResult", "TestProcedure", "MeasurementMetrics", "TestEnvironment", "AcceptanceCriteria", "Status"]
                        }
                        onUpdate={handlePropertyUpdate}
                      />
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full p-4">
                <EmptyStateView type="testCase" />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TestCaseDetails; 
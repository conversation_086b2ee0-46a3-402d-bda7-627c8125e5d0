"use client";
import React, { useState, useEffect } from "react";
import { Clock, ChevronDown, ChevronRight, ExternalLink } from "lucide-react";
import { usePathname } from "next/navigation";

// Sample test data - this would typically come from an API
const testCasesData = {
  "1": {
    id: 1,
    title: "Verify accuracy of temperature prediction algorithm",
    priority: "HIGH",
    estimate: "4m",
    type: "Smoke",
    userStory: {
      id: "US-123",
      title: "Weather prediction accuracy improvements",
      link: "/project/123/user-story/US-123"
    },
    description: "This test case aims to validate the accuracy of the temperature prediction algorithm by comparing the predicted temperatures against actual recorded temperatures for a given set of locations over a specific time period.",
    expected: {
      intro: "Etiam massa dolor, ornare sit amet, lacinia nec, bibendum ut, magna.",
      list: [
        "Nam feugiat, eros at commodo dictum,",
        "Felis libero varius orci, in vulputate",
        "Massa turpis scelerisque diam.",
        "Nunc et felis est. Phasellus laoreet nibh vel augue",
        "Faucibus at varius est pretium."
      ],
      conclusion: "Quisque pellentesque mauris."
    },
    steps: [
      { number: 1, title: "Open projects page:" },
      {
        number: 2,
        title: 'Click on button "Create new project"',
        expectedResult: "You are being redirected to the new project form",
      },
      {
        number: 3,
        title: "Fill the form with the following credentials:",
        details: [
          "Project name: Regression",
          "Description: Project description",
          "Project type: Web",
        ],
      },
      { number: 4, title: 'Click on "Create project" button' },
    ]
  },
  // Add more test cases as needed
};

const TestCaseDetails = () => {
  const [expandedSteps, setExpandedSteps] = useState([1, 2, 3, 4]);
  const [testCase, setTestCase] = useState(null);
  const [testcaseType, setTestcaseType] = useState("");
  const pathname = usePathname();
  
  useEffect(() => {
    if (pathname) {
      // Extract test case ID and type from URL
      const urlParts = pathname.split("/");
      const testCaseId = urlParts[5] || "";
      const testCaseType = urlParts[5] || "general";
      
      setTestcaseType(testCaseType);
      
      // Get test case data by ID
      if (testCasesData[testCaseId]) {
        setTestCase(testCasesData[testCaseId]);
      } else if (Object.keys(testCasesData).length > 0) {
        // Use the first test case if the name doesn't match
        setTestCase(testCasesData[Object.keys(testCasesData)[0]]);
      }
    }
  }, [pathname]);

  const toggleStep = (stepNumber) => {
    setExpandedSteps((prev) =>
      prev.includes(stepNumber)
        ? prev.filter((step) => step !== stepNumber)
        : [...prev, stepNumber]
    );
  };

  if (!testCase) {
    return <div>Loading test case data...</div>;
  }

  return (
    <div className="pb-16">
      {/* User Story Box */}
      <div className="mb-6 border border-blue-100 bg-blue-50 rounded-md p-4">
        <div className="flex items-start justify-between">
          <div>
            <div className="typography-body-sm text-blue-700 font-weight-medium mb-1">User Story</div>
            <div className="flex items-center">
              <span className="font-weight-medium text-gray-800">{testCase.userStory.id}</span>
              <span className="mx-2 text-gray-500">•</span>
              <span className="text-gray-800">{testCase.userStory.title}</span>
            </div>
          </div>
          <a 
            href={testCase.userStory.link}
            className="flex items-center gap-1 text-blue-600 hover:text-blue-800 typography-body-sm"
          >
            <span>Open User Story</span>
            <ExternalLink size={14} />
          </a>
        </div>
      </div>

      {/* Priority, Estimate, Type Section */}
      <div className="grid grid-cols-2 gap-4 text-left mb-6">
        <div className="flex items-center">
          <span className="project-panel-heading w-24">Priority</span>
          <span className="rounded-lg px-2 py-1 bg-gray-200 text-font">
            {testCase.priority}
          </span>
        </div>

        <div className="flex items-center">
          <span className="project-panel-heading w-24">Estimate</span>
          <div className="font-weight-medium px-2 py-1 flex items-center">
            <Clock size={14} className="mr-1 text-gray-400" />
            {testCase.estimate}
          </div>
        </div>

        <div className="flex items-center">
          <span className="project-panel-heading w-24">TYPE</span>
          <span className="rounded-lg px-2 py-1 text-font">{testCase.type}</span>
        </div>
      </div>

      {/* Description Section */}
      <div className="mb-6">
        <label className="block typography-heading-6 text-gray-800 mb-1">
          Description
        </label>
        <p className="typography-body-sm text-gray-600">{testCase.description}</p>
      </div>

      {/* Expected Results Section */}
      <div className="mb-6">
        <label className="block typography-heading-6 text-gray-800 mb-1">
          Expected
        </label>
        <p className="typography-body-sm text-gray-600 mb-2">{testCase.expected.intro}</p>
        <ul className="list-disc pl-5 typography-body-sm text-gray-600 mb-2">
          {testCase.expected.list.map((item, index) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
        <p className="typography-body-sm text-gray-600">{testCase.expected.conclusion}</p>
      </div>
      
      {/* Steps Section */}
      <div className="mb-6">
        <h6 className="block font-weight-bold text-gray-800 mb-1">
          Steps to Create a New Project
        </h6>
        {testCase.steps.map((step) => (
          <div key={step.number} className="mb-2">
            <div
              className="flex items-center cursor-pointer"
              onClick={() => toggleStep(step.number)}
            >
              {expandedSteps.includes(step.number) ? (
                <ChevronDown className="mr-2 text-gray-600" size={20} />
              ) : (
                <ChevronRight className="mr-2 text-gray-600" size={20} />
              )}
              <span className="font-weight-medium text-gray-600">
                {step.number}. {step.title}
              </span>
            </div>
            {expandedSteps.includes(step.number) && step.expectedResult && (
              <div className="ml-6 mt-2">
                <p className="typography-body-sm text-gray-600">Expected result</p>
                <p className="typography-body-sm text-gray-600">{step.expectedResult}</p>
              </div>
            )}
            {expandedSteps.includes(step.number) && step.details && (
              <div className="ml-6 mt-2 text-gray-600">
                {step.details.map((detail, index) => (
                  <p key={index} className="typography-body-sm">
                    {detail}
                  </p>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default TestCaseDetails;
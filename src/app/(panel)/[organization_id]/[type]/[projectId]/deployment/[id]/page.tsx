//@ts-nocheck
"use client";

import React, { useState, useEffect ,useContext} from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import DiscussionModal from "../components/Discussion";
import Overview from "../components/Overview"
import DeploymentEnvironment from "../components/DeploymentEnvironment";
import { fetchNodeById } from "@/utils/api";
import {  Plus } from "lucide-react";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import ConfigureModal from "@/components/Modal/ConfigureModel";
import { StateContext } from "@/components/Context/StateContext";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { useDeployment } from "@/components/Context/DeploymentContext";
import UserInput from "@/components/Deployment/UserInput";

const NoSSR = dynamic(
  () => import("@/components/Chart/MermaidChart"),
  { ssr: false }
);
;
interface TabProps {
  label: string;
  isActive: boolean;
  onClick: () => void;
}

const TabComponent: React.FC<TabProps> = ({ label, isActive, onClick }) => (
  <div
    role="tab"
    aria-selected={isActive}
    onClick={onClick}
    className={`flex items-center gap-3 p-2 px-4 cursor-pointer transition-all ${
      isActive
        ? 'text-semantic-gray-900 bg-white rounded-custom-md shadow-[0.059375rem_0.059375rem_0.1625rem_rgba(0,0,0,0.15)] border border-custom-border'
        : 'text-semantic-gray-600 bg-custom-bg-secondary rounded-custom-md hover:bg-semantic-gray-100'
    }`}
  >
    <span
      className="overflow-hidden text-ellipsis leading-none transition-all ease-in-out whitespace-nowrap"
    >
      {label}
    </span>
  </div>
);

const FrontendPage = () => {
  const { response } = useDeployment();
  const [showData, setShowData] = useState(response);
  const [activeTab, setActiveTab] = useState("overview");
  const [nodeTitle, setNodeTitle] = useState("");
  const [isDiscussionOpen, setIsDiscussionOpen] = useState(false);
  const [configureModel, setConfigureModel] = useState(false);
  const { setIsVertCollapse } = useContext(StateContext);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const { showAlert } = useContext(AlertContext);

  const [expandedSections, setExpandedSections] = useState({
    summary: true,
    overview: true,
  });

  const router = useRouter();
  const pathname = usePathname();

  const nodeId = pathname.split("/").pop();

  const extractIds = (path: string) => {
    const segments = path.split('/'); 
    const projectId = segments[2]; 
    const containerId = segments[4]; 

    return { projectId, containerId };
  };

  const { projectId, containerId } = extractIds(pathname);
  const [isDeploying, setIsDeploying] = useState(false);
  const [deploymentSteps, setDeploymentSteps] = useState([]);
  const [error, setError] = useState(null);
  const [completed, setCompleted] = useState(false);
  const handleConfigureClick = () => {
    setConfigureModel(true);
  };

  const handleCloseModal = () => {
    setConfigureModel(false);
  };

  const tabs = [
    { id: "userinput", label: "User Input" },
    { id: "overview", label: "Overview" },
    { id: "environment", label: "Deployment Environment" },
    { id: "visualization", label: "Infrastructure visualization" },
  ];

  // Fetch node title on component mount
  useEffect(() => {
    const fetchNodeTitle = async () => {
      try {
        const response = await fetchNodeById(nodeId, "Container");
        if (response && response.properties && response.properties.Title) {
          setNodeTitle(response.properties.Title);
        }
      } catch (error) {
        
        setNodeTitle("Infrastructure"); // Fallback title
      }
    };

    if (nodeId) {
      fetchNodeTitle();
    }
  }, [nodeId]);


  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
  };
  
  const handleCreateInfrastructure = async () => {
    setIsDeploying(true);
    setError(null);
    setDeploymentSteps([]);
    
    try {
      const response = await fetch(
        `http://localhost:8000/api/auth/deployment/${projectId}/${containerId}/create_or_update_deployment_node`,
        {
          method: 'POST',
        }
      );

      if (!response.ok) {
        throw new Error('Deployment request failed');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(5));
              
              if (data.status === 'error') {
                setError(data.message);
                break;
              }

              setDeploymentSteps(prev => [...prev, data]);

              if (data.status === 'completed' && data.step === 'deployment') {
                setCompleted(true);
                if (onComplete) {
                  onComplete(data.data);
                }
              }
            } catch (e) {
              
            }
          }
        }
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsDeploying(false);
    }
  };

  const renderActionButtons = () => {
    switch (activeTab) {
      case "overview":
        return (
          <>
            <DynamicButton
              variant="primary"
              size="medium"
              icon={Plus}
              text="Create Infrastructure"
              onClick={handleCreateInfrastructure}
              tooltip="Create new infrastructure"
            />
            <DynamicButton
            type="submit"
            size="medium"
            variant="primaryOutline"
            icon={Plus}
            onClick={handleConfigureClick}
            text="Auto Configure"
            className="mt-[5px]"
          />
          </>
        );
     
      default:
        return null;
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "userinput":
        return <UserInput showData={UserInput} />;
      case "overview":
        return <Overview showData={showData} />;
      case "environment":
        return <DeploymentEnvironment />;
      // case "visualization":
      //   return <div>Infrastructure Visualization Content</div>;
      default:
        return null;
    }
  };

  

  return (
    <div className="overflow-auto flex flex-col bg-white mt-1">
      {/* Header Section - Fixed */}
      <div className="flex-none bg-white">
        <div className="flex justify-between items-start py-2">
          {/* Tabs Container */}
          <div className="bg-[#f9f9fb] rounded-md px-1.5">
            <div className="flex gap-2">
              {tabs.map((tab) => (
                <TabComponent
                  key={tab.id}
                  label={tab.label}
                  isActive={activeTab === tab.id}
                  onClick={() => handleTabClick(tab.id)}
                />
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2 mr-2">
            {renderActionButtons()}
          </div>
        </div>

        {/* Breadcrumb */}
        <div className="h-[36px] flex items-center px-4">
          <div className="flex items-center typography-body-sm">
            {/* <House /> */}
            <Link href="." className="text-gray-600 hover:text-blue-600 text-[0.8rem] font-weight-normal">
              Home
            </Link>
            <span className="mx-2 text-[#2A3439]">›</span>
            <span className="text-gray-600 hover:text-blue-600 text-[0.8rem] font-weight-normal">{nodeTitle}</span>
          </div>
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto -mt-4">
        <div className="px-1 py-1">
          <div className="mx-auto">
            {/* Conditional Rendering */}
            {/* Scrollable Content Area */}
            <div className="flex-1 overflow-y-auto">{renderTabContent()}</div>
          </div>
        </div>
      </div>
      {/* Discussion Modal */}
      <DiscussionModal
        isOpen={isDiscussionOpen}
        onClose={() => setIsDiscussionOpen(false)}
        nodeId="placeholder-id"
        nodeType="Infrastructure"
      />

{configureModel && (
    <ConfigureModal
      id={projectId}
      type={"Architecture"}
      isNodeType={"Architecture"}
      closeModal={handleCloseModal}
      setLoadingAutoConfigure={setLoadingAutoConfigure}
      onSubmitSuccess={() => {
        showAlert("Container Configured Successfully", "success");
        setIsVertCollapse(false);
      }}
    />
  )}
    </div>
  );
};

export default FrontendPage;
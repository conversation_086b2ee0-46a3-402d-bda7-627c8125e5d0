"use client";

import React, { useEffect, useState, useContext } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { getOauthCallback } from '@/utils/api';
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { Loader2 } from "lucide-react";

const Page: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const searchParams = useSearchParams();
  const code = searchParams.get('code');
  const state = searchParams.get('state');
  const router = useRouter();
  const { showAlert } = useContext(AlertContext);

  const fetchRepositories = async () => {
    const scm_type = sessionStorage.getItem("providerId");
    
    try {
      setIsLoading(true);
      let scm_to_pass = scm_type? scm_type : "github";
      const OauthResponse = await getOauthCallback(scm_to_pass, state, code);
      let encryptedSCMId = OauthResponse.data.encrypted_scm_id;
      
      if (OauthResponse.status === "success") {
        const escapedMessage = OauthResponse.message
          .replace(/'/g, "&apos;") // Escape single quotes
          .replace(/"/g, "&quot;"); // Escape double quotes
        
        showAlert(escapedMessage, "success");

        if (window.opener) {
          // Call your backend to complete the OAuth flow, then:
          window.opener?.postMessage('github_connected', '*');
          setTimeout(() => window.close(), 1000);
        }else
        {

          router.push(`/dashboard/scm/connection?scmType=${scm_type}&scmId=${encodeURIComponent(encryptedSCMId)}`);
        }

      }
    } catch (error) {
      
      setTimeout(() => window.close(), 2000);
    
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRepositories();
  }, [state, code]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      {isLoading ? (
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full mx-4">
          <div className="flex flex-col items-center space-y-6">
            <div className="relative">
              <Loader2 className="w-12 h-12 animate-spin text-blue-500" />
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent animate-pulse" />
            </div>
            
            <div className="space-y-2 text-center">
              <h3 className="typography-body-lg font-weight-semibold text-gray-900">
                Processing Repositories
              </h3>
              <div className="space-y-1">
                <p className="typography-body-sm text-gray-600">
                  We're securely connecting to your repositories
                </p>
                <p className="typography-caption text-gray-500">
                  This may take a few moments
                </p>
              </div>
            </div>

            <div className="w-full h-1 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className="h-full bg-blue-500 rounded-full animate-progress-infinite"
                style={{
                  animation: 'progress-infinite 2s ease-in-out infinite',
                }}
              />
            </div>

            <style jsx>{`
              @keyframes progress-infinite {
                0% {
                  width: 0%;
                  margin-left: 0%;
                }
                50% {
                  width: 100%;
                  margin-left: 0%;
                }
                100% {
                  width: 0%;
                  margin-left: 100%;
                }
              }
            `}</style>
          </div>
        </div>
      ) : (
        <div>Your repositories or further content would be displayed here</div>
      )}
    </div>
  );
};

export default Page;

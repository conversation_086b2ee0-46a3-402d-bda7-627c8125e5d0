"use client";
import React, {
  useState,
  useEffect,
  useContext,
  useCallback,
  useRef,
} from "react";
import {
  FaTimes,
  FaExpand,
  FaCompress,
  FaChevronCircleDown,
} from "react-icons/fa";
import {
  getTitleForRelationship,
  groupRelationshipsByType,
  renderHTML,
} from "@/utils/helpers";
import { formatUTCToLocal } from "@/utils/datetime";
import DiscussionChatPanel from "@/components/DiscussionChatPanel";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import ApproversListModal from "./ApproversListModal";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/Accordion";
import ModificationActionButtons from "@/components/Buttons/ModificationActionButton";
import dynamic from "next/dynamic";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { fetchNodeBasedOnDataModelById, getComponentDeployments } from "@/utils/api"; // Import your API functions
import {
  FaEye,
  FaUserCog,
} from "react-icons/fa";
import Overview from "@/components/Deployment/Overview";
import Code from "@/components/Deployment/Code";
// import Terminal from "@/components/Deployment/Terminal";
// import Steps from "@/components/Deployment/Steps";
import UserInput from "@/components/Deployment/UserInput";
import {
  mergeModificationRequest,
  getDiscussionModifications,
} from "@/utils/discussionAPI";
import { DiscussionChatContext } from "@/components/Context/DiscussionChatContext";
import { useSearchParams } from "next/navigation";
import Cookies from "js-cookie";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import { TopBarContext } from "@/components/Context/TopBarContext";
import { PanelContext } from "@/components/Context/PanelContext";
import NodeInfo from "@/components/Discussion/NodeInfo";
import UsersModal from "./UsersModal.jsx";
import Badge from "@/components/UIComponents/Badge/Badge";
import "@/styles/tabs/discussionModal.css";
import { DiscussionTabContext } from "@/components/Context/DiscussionTabContext";
import Editor from '@monaco-editor/react';
import { useRouter, usePathname } from "next/navigation";

const NoSSR = dynamic(() => import("../../components/Chart/MermaidChart"), {
  ssr: false,
});

const CloseModal = ({ onClose, onConfirm, pathname, setDiscussionId, showAlert }) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const router = useRouter();

  const handleConfirm = () => {
    setIsUpdating(true);
    onConfirm();
    setIsUpdating(false);
    onClose();
    setDiscussionId(null);
    router.replace(pathname);
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center z-[60]">
      <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={onClose} />
      <div className="bg-white rounded-lg shadow-xl w-lg p-8 z-[70] relative">
        <div className="space-y-8">
          <div className="text-center">
            <h3 className="typography-body-lg font-weight-semibold text-gray-900">
              Close Discussion
            </h3>
            <p className="mt-2 typography-body-sm text-gray-500">
              Are you sure you want to close this discussion?
            </p>
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-2 gap-3 mt-6">
            <button
              onClick={onClose}
              className="px-4 py-2 typography-body-sm font-weight-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none"
            >
              Cancel
            </button>
            <button
              onClick={() => handleConfirm()}
              disabled={isUpdating}
              className="px-4 py-2 typography-body-sm font-weight-medium text-white bg-red-600 hover:bg-red-700 rounded-md focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isUpdating
                ? 'Closing...'
                : 'Close'
              }
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

function Modal() {
  const {
    isOpen,
    onClose,
    onFullScreen,
    isFullScreen,
    steps,
    setSteps,
    setIsDiscussionModalOpen,
    handleStepExecution,
    discussionId,
    setDiscussionId,
    modifications,
    setModifications,
    processing,
    nodeId,
    nodeType,
    allUsers,
    setMessages,
    messages
  } = useContext(DiscussionChatContext);

  const router = useRouter();
  const pathname = usePathname();

  const [nodeInfo, setNodeInfo] = useState(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const searchParams = useSearchParams();
  const [mergeStep, setMergeStep] = useState({});
  const [mergeStepIndex, setMergeStepIndex] = useState(-1);
  const [activeTab, setActiveTab] = useState("Overview");
  const { updateTabTitle } = useContext(TopBarContext);
  const { updateProjectTitle } = useContext(PanelContext);
  const { deploymentId } = useContext(DiscussionTabContext);

  const [uiMetaData, SetUiMetaData] = useState({});
  const { showAlert } = useContext(AlertContext);
  const bottomRef = useRef(null);
  const [isUsersModalOpen, setIsUsersModalOpen] = useState(false);
  const [openAccordionIndexes, setOpenAccordionIndexes] = useState([]);
  const [nodeInfoAbortController, setNodeInfoAbortController] = useState(new AbortController())
  const modalClosed = useRef(false);
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [isMergeLoading, setIsMergeLoading] = useState(false);

  const renderMarkdownContent = (content) => {
    if (typeof content !== "string") return content;

    // Use the centralized renderHTML function for consistent markdown processing
    return renderHTML(content);
  };

  const tabs = [
    { label: "Overview", icon: FaEye },
    // { label: "Code", icon: FaFileCode },
    // { label: "Terminal", icon: FaCode },
    // { label: "Steps", icon: FaList },
    // { label: "Infrastructure Visualisation", icon: FaProjectDiagram },
    { label: "User Input", icon: FaUserCog },
  ];

  useEffect(() => {
    if (modifications.length > 0) {
      setOpenAccordionIndexes([modifications.length - 1]);
    }
  }, [modifications]);

  const handleUsersClick = () => {
    setIsDropdownOpen(false);
    setIsUsersModalOpen(true);
  };

  useEffect(() => {
    const fetchModifications = async () => {
      try {
        // Don't fetch modifications if in code query mode
        if (searchParams.get('sessionId')) {
          return;
        }

        const data = await getDiscussionModifications(discussionId);

        setModifications(data);
      } catch (error) {

        showAlert("Failed to fetch modifications", "error");
      }
    };

    if (discussionId) {
      fetchModifications();
    }
  }, [mergeModificationRequest, discussionId]);


  const fetchNodeInfo = useCallback(async () => {
    try {
      if (
        searchParams.get("discussion") === "new" &&
        searchParams.get("discussionType") === "Configuration" &&
        searchParams.get("node_type") === "Deployment"
      ) {
        // Get projectId and containerId from URL
        const urlParams = new URLSearchParams(window.location.search);
        const containerId = urlParams.get('node_id');
        const projectId = window.location.pathname.split('/')[2];

        // First fetch the deployment ID
        const deploymentData = await getComponentDeployments(projectId, containerId);
        const deploymentId = deploymentData.deployments[0].id;

        // Then use this deployment ID to fetch node info
        const data = await fetchNodeBasedOnDataModelById(deploymentId, "Deployment");
        setNodeInfo(data);
        SetUiMetaData(data?.ui_metadata);
      } else {
        // Default case - use props
        if (nodeInfo) {
          return
        }
        /*if (nodeInfoAbortController.signal.aborted){
          return
        }*/
        nodeInfoAbortController.abort()
        const data = await fetchNodeBasedOnDataModelById(nodeId, nodeType);
        setNodeInfo(data);
        SetUiMetaData(data?.ui_metadata);
      }
    } catch (error) {
      setNodeInfoAbortController(new AbortController())

      showAlert("Failed to fetch node information", "error");
    }
  }, [nodeId, nodeType, searchParams]);

  useEffect(() => {
    // Scroll to bottom when modifications change
    bottomRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [modifications]);


  useEffect(() => {
    setModifications([]);
    if (
      isOpen() &&
      ((nodeId && nodeType) ||
        (searchParams.get("discussion") === "new" &&
          searchParams.get("discussionType") === "Configuration" &&
          searchParams.get("node_type") === "Deployment"))
    ) {
      fetchNodeInfo();
    }
  }, [nodeId, nodeType, searchParams]);
  useEffect(() => {
    // Check for query parameters on initial load
    if (!modalClosed.current) {
      if (
        searchParams.get("discussion") === "existing" &&
        searchParams.get("discussion_id")
      ) {
        setIsDiscussionModalOpen(true);
        setDiscussionId(Number(searchParams.get("discussion_id")));
      } else if (searchParams.get("discussion") === "new") {
        setIsDiscussionModalOpen(true);
      } else {
        setIsDiscussionModalOpen(false);
      }
    }
    else {
      setIsDiscussionModalOpen(false);
      modalClosed.current = false;
    }
  }, [searchParams, discussionId]); // Include discussionId as a dependency

  useEffect(() => {
    steps.forEach((step, index) => {
      if (step.name == "merge_captured_items") {
        setMergeStep(step);
        setMergeStepIndex(index);
      }
    });
  }, [modifications]);

  if (!isOpen()) return <></>;

  const handleClose = async (reRenderCurrentPath = false) => {
    setNodeInfo(null);
    modalClosed.current = true;
    onClose();

    if (reRenderCurrentPath) {
      router.replace(pathname);
      setTimeout(() => {
        window.location.href = pathname;
      }, 100);
    }
  }

  const mergeCloseFunction = async (nodeId, modifications, mergeStep, mergeStepIndex, index) => {
    setIsMergeLoading(true)
    try {
      updateTabTitle(
        nodeId,
        modifications
      );
      await updateProjectTitle(
        nodeId,
        modifications
      );
      // showAlert("Updating project details...", "info");
      await handleStepExecution(
        mergeStep,
        mergeStepIndex,
        null,
        "repeat",
        {},
        index
      );
      setNodeInfo(null);
      setDiscussionId(null);
      modalClosed.current = true;
      onClose();
    } catch (error) {
      console.error("Error during merge:", error);

    } finally {
      setIsMergeLoading(false);
    }

  }

  const handleBackgroundClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  const handleReadyToMergeClick = (e, index) => {
    e.stopPropagation(); // Prevent double triggering

    // Add this index to openAccordionIndexes if it's not already there
    setOpenAccordionIndexes((prev) => {
      const newIndexes = new Set(prev);
      newIndexes.add(index.toString());
      return Array.from(newIndexes);
    });

    // Wait for accordion to open, then scroll
    setTimeout(() => {
      bottomRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };

  const renderField = (key, value, uiMetaData) => {
    if (!value) return null;

    // List of keys that should use Monaco Editor with their corresponding languages
    const cleanJsonString = (str) => {
      try {
        // Remove <JSON> tags and decode Unicode characters
        const cleanStr = str
          .replace(/\\u003cJSON\\u003e|<JSON>/, '')
          .replace(/\\u003c\/JSON\\u003e|<\/JSON>/, '')
          .replaceAll('<JSON>', '') // Plain <JSON>
          .replaceAll('</JSON>', '')
          .replace(/\\n/g, '\n')
          .replace(/\\"/g, '"');

        // Parse and stringify to format it properly
        const parsed = JSON.parse(cleanStr);
        return JSON.stringify(parsed, null, 2);
      } catch (e) {

        return str;
      }
    };

    // List of keys that should use Monaco Editor with their corresponding languages
    const monacoEditorConfig = {
      'main_tf': 'hcl',
      'outputs_tf': 'hcl',
      'providers_tf': 'hcl',
      'variables_tf': 'hcl',
      'workflow_file': 'yaml',
      'docker_file': 'dockerfile',
      'build_spec': 'yaml',  // Adding this for build spec content
      'build_settings': 'yaml'  // Adding this if you have build settings
    };

    // Check if field should use Monaco Editor
    const isJsonContent = (str) => {
      // Handle null, undefined, or non-string inputs
      if (!str || typeof str !== 'string') {
        return false;
      }

      try {
        return (
          (str.includes('"openapi"') || str.includes('\\"openapi\\"')) ||
          (str.includes('<JSON>') || str.includes('\\u003cJSON\\u003e'))
        );
      } catch (error) {
        // Handle any unexpected errors during string operations

        return false;
      }
    };


    // Determine if we should use Monaco Editor
    const shouldUseMonaco = key.endsWith('_tf') ||
      monacoEditorConfig[key] ||
      isJsonContent(value);

    if (shouldUseMonaco) {
      const language = isJsonContent(value) ? 'json' : (monacoEditorConfig[key] || 'hcl');
      const processedValue = isJsonContent(value) ? cleanJsonString(value) : value;


      return (
        <div key={key} className="space-y-3">
          <h3 className="text-[15px] font-weight-bold text-gray-800">
            {key}
          </h3>
          <div className="pl-4">
            <Editor
              height="400px"  // Increased height for better JSON visibility
              defaultLanguage={language}
              defaultValue={processedValue}
              theme="vs-dark"
              options={{
                readOnly: true,
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                lineNumbers: "on",
                automaticLayout: true,
                fontSize: 'var(--font-size-code)',
                formatOnPaste: true,
                formatOnType: true,
                wordWrap: "on"
              }}
            />
          </div>
        </div>
      );
    }



    const flattenMetadata = (metadata) => {
      if (!metadata || typeof metadata !== 'object') {
        return {};
      }
      const flattened = {};

      // Iterate through each top-level key (Design, ClassDiagram, Diagram, etc.)
      Object.values(metadata).forEach((section) => {
        // Merge each section's fields into the flattened object
        Object.entries(section).forEach(([key, value]) => {
          // Only add if the key doesn't already exist (first occurrence takes precedence)
          if (!flattened[key]) {
            flattened[key] = value;
          }
        });
      });

      return flattened;
    };

    const flattenedMetadata = flattenMetadata(uiMetaData);

    if (flattenedMetadata[key]?.display_type === "mermaid_chart") {
      return (
        <div key={key} className="space-y-2">
          <h3 className="text-[15px] font-weight-bold text-gray-800">
            {flattenedMetadata[key].Label ||
              key.charAt(0).toUpperCase() +
              key
                .slice(1)
                .replace(/([A-Z])/g, " $1")
                .trim()}
          </h3>
          <div className="pl-4">
            <NoSSR chartDefinition={value} />
          </div>
        </div>
      );
    }

    if (key === "Diagram") {
      return (
        <div key={key} className="space-y-2">
          <h3 className="text-[15px] font-weight-bold text-gray-800">
            {
              key.charAt(0).toUpperCase() +
              key
                .slice(1)
                .replace(/([A-Z])/g, " $1")
                .trim()}
          </h3>
          <div className="pl-4">
            <NoSSR chartDefinition={value} />
          </div>
        </div>
      );
    }

    // Handle rich text display type
    if (flattenedMetadata[key]?.display_type === "rich_text") {
      return (
        <div key={key} className="space-y-3">
          <h3 className="text-[15px] font-weight-bold text-gray-800">
            {key.charAt(0).toUpperCase() +
              key
                .slice(1)
                .replace(/([A-Z])/g, " $1")
                .trim()}
          </h3>
          <div
            className="text-gray-600 pl-4"
            dangerouslySetInnerHTML={{
              __html: renderMarkdownContent(value),
            }}
          />
        </div>
      );
    }

    // Default rendering for other types
    return (
      <div key={key} className="space-y-3">
        <h3 className="text-[15px] font-weight-bold text-gray-800">
          {flattenedMetadata[key]?.Label ||
            key.charAt(0).toUpperCase() +
            key
              .slice(1)
              .replace(/([A-Z])/g, " $1")
              .trim()}
        </h3>
        <div
          className="text-gray-600 pl-4"
          dangerouslySetInnerHTML={{
            __html: renderMarkdownContent(value),
          }}
        />
      </div>
    );
  };

  return (
    <>
      <div
        className={`fixed discussion-modal inset-0 backdrop-blur-modal flex justify-center items-center z-50 ${isFullScreen ? "w-full h-full" : ""
          }`}
        onClick={handleBackgroundClick}
      >
        <div
          className={`bg-[#ffffff] ${isFullScreen ? "w-full h-full" : "w-[95%] h-[90%]"
            } p-4 flex flex-col relative`}
        >
          {isUsersModalOpen && (
            <UsersModal
              isOpen={isUsersModalOpen}
              onClose={() => setIsUsersModalOpen(false)}
              discussionId={discussionId}
            />
          )}
          <div className="flex gap-1 flex-grow overflow-hidden">
            <div className="min-w-[30%] max-w-[30%] border border-gray-200  p-1 rounded-md">
              <DiscussionChatPanel
                setModifications={setModifications}
                discussionId={discussionId}
                setDiscussionId={setDiscussionId}
              />
            </div>
            <div className="w-full max-h-full overflow-auto custom-scrollbar rounded-md border border-gray-200 flex flex-col space-y-2">
              <div className="w-full max-h-full overflow-auto custom-scrollbar p-2 rounded-md  flex flex-col space-y-2">
                {searchParams.get("discussion") === "new" &&
                  searchParams.get("discussionType") === "Configuration" ? (
                  <>
                    <div className="sticky top-0 bg-white border-gray-200 z-10 ">
                      <div className="flex items-center">
                        <div className="flex space-x-2">
                          {tabs.map(({ label, icon: Icon }) => (
                            <button
                              key={label}
                              role="tab"
                              aria-selected={activeTab === label}
                              className={`flex items-center gap-1 py-1 px-2 cursor-pointer whitespace-nowrap rounded-md ${activeTab === label
                                ? "text-[#1c64f2] shadow-sm border border-gray-200"
                                : "text-gray-500 hover:bg-gray-100"
                                }`}
                              onClick={() => setActiveTab(label)}
                            >
                              <Icon size={16} className="text-gray-500" />
                              <span className="ml-1">{label}</span>
                            </button>
                          ))}
                        </div>
                        <div className="ml-auto">
                          <DynamicButton
                            variant="secondary"
                            icon={FaTimes}
                            onClick={() => setShowCloseModal(true)}
                            tooltip="Close"
                            size="sqDefault"
                          />
                        </div>
                      </div>
                    </div>
                    <div className="flex-grow overflow-auto justify-between">
                      <div className="flex flex-col h-[75vh] bg-opacity-20 ">
                        {/* Main Tabs and Control Buttons */}
                        <div className="tab-buttons flex flex-col border border-gray-200"></div>

                        {/* Content Area */}
                        <div className="overflow-auto custom-scrollbar flex-grow">
                          <div className="tab-content p-4">
                            {activeTab === "Overview" && (
                              <>
                                {/* <Overview /> */}
                                {searchParams.get("node_type") ===
                                  "Deployment" ? (
                                  <div className="flex-1 overflow-y-auto custom-scrollbar">
                                    <NodeInfo nodeInfo={nodeInfo} />
                                    <div className="border border-gray-200 rounded-md overflow-hidden bg-white mt-3">
                                      {modifications.map(
                                        (modification, index) => {
                                          const isLast =
                                            index === modifications.length - 1;
                                          if (!modification.modified_node) {
                                            return null;
                                          }
                                          return (
                                            <Accordion
                                              items={modifications}
                                              key={index}
                                            >
                                              <AccordionItem
                                                value={index.toString()}
                                              >
                                                <AccordionTrigger className="text-gray-700 font-weight-semibold px-4 py-3 bg-gray-50 hover:bg-gray-100">
                                                  <div className="flex w-full justify-between items-center">
                                                    <div className="text-left">
                                                      <div className="project-panel-heading">
                                                        {modification
                                                          .modified_node
                                                          .Title ||
                                                          nodeInfo?.node
                                                            ?.Title ||
                                                          "Updated Node"}
                                                        {modification
                                                          .modified_node
                                                          ?.Type && (
                                                            <Badge
                                                              className="bg-blue-100 text-blue-800"
                                                              type={
                                                                modification
                                                                  .modified_node
                                                                  ?.Type
                                                              }
                                                            />
                                                          )}
                                                      </div>
                                                      <p className="typography-body-sm text-gray-500">
                                                        {formatUTCToLocal(
                                                          modification.created_at
                                                        )}
                                                      </p>
                                                    </div>

                                                    <div className="flex items-center gap-2">
                                                      <div className="flex items-center gap-2">
                                                        <div className="w-2 h-2 bg-green-500 rounded-full animate-ping" />
                                                        <a
                                                          href="#"
                                                          onClick={(e) => {
                                                            e.preventDefault();
                                                            handleReadyToMergeClick(e, index);
                                                          }}
                                                          title="Click to expand and scroll to merge changes"
                                                          className="text-green-700 hover:underline flex items-center typography-body-sm"
                                                        >
                                                          <span>Ready to merge</span>
                                                          <FaChevronCircleDown className="ml-1 group-hover:animate-bounce" />
                                                        </a>
                                                      </div>
                                                    </div>
                                                  </div>
                                                </AccordionTrigger>
                                                <AccordionContent>
                                                  <div className="bg-white rounded-lg">
                                                    <div className="space-y-6">
                                                      {/* Main Content */}
                                                      {Object.entries(
                                                        modification.modified_node
                                                      ).map(([key, value]) => {
                                                        if (
                                                          [
                                                            "id",
                                                            "Title",
                                                            "Type",
                                                            "__typename",
                                                          ].includes(key)
                                                        )
                                                          return null;

                                                        return renderField(
                                                          key,
                                                          value,
                                                          uiMetaData
                                                        );
                                                      })}

                                                      {/* New Child Nodes */}
                                                      {modification.new_child_nodes &&
                                                        modification
                                                          .new_child_nodes
                                                          .length > 0 && (
                                                          <div className="mt-8">
                                                            <h3 className="typography-heading-4 text-gray-800 mb-4">
                                                              New Child Nodes
                                                            </h3>
                                                            <div className="space-y-6">
                                                              {modification.new_child_nodes.map(
                                                                (
                                                                  child,
                                                                  index
                                                                ) => (
                                                                  <div
                                                                    key={index}
                                                                    className="p-4 bg-gray-50 rounded-lg border border-gray-200"
                                                                  >
                                                                    <h4 className="typography-body font-weight-semibold text-gray-700 mb-3">
                                                                      {
                                                                        child.Title
                                                                      }
                                                                      {child.Type && (
                                                                        <Badge
                                                                          className="ml-2 bg-blue-100 text-blue-800"
                                                                          type={
                                                                            child.Type
                                                                          }
                                                                        />
                                                                      )}
                                                                    </h4>
                                                                    <div className="space-y-4">
                                                                      {Object.entries(
                                                                        child
                                                                      ).map(
                                                                        ([
                                                                          key,
                                                                          value,
                                                                        ]) => {
                                                                          if (
                                                                            [
                                                                              "id",
                                                                              "Title",
                                                                              "Type",
                                                                              "__typename",
                                                                            ].includes(
                                                                              key
                                                                            )
                                                                          )
                                                                            return null;

                                                                          return renderField(
                                                                            key,
                                                                            value,
                                                                            uiMetaData
                                                                          );
                                                                        }
                                                                      )}
                                                                    </div>
                                                                  </div>
                                                                )
                                                              )}
                                                            </div>
                                                          </div>
                                                        )}

                                                      {/* Modified Child Nodes */}
                                                      {modification.modified_children &&
                                                        modification
                                                          .modified_children
                                                          .length > 0 && (
                                                          <div className="mt-8">
                                                            <h3 className="typography-heading-4 text-gray-800 mb-4">
                                                              Modified Child
                                                              Nodes
                                                            </h3>
                                                            <div className="space-y-6">
                                                              {modification.modified_children.map(
                                                                (
                                                                  child,
                                                                  index
                                                                ) => (
                                                                  <div
                                                                    key={index}
                                                                    className="p-4 bg-gray-50 rounded-lg border border-gray-200"
                                                                  >
                                                                    <h4 className="typography-body font-weight-semibold text-gray-700 mb-3">
                                                                      {
                                                                        child.Title
                                                                      }
                                                                      {child.Type && (
                                                                        <Badge
                                                                          className="ml-2 bg-blue-100 text-blue-800"
                                                                          type={
                                                                            child.Type
                                                                          }
                                                                        />
                                                                      )}
                                                                    </h4>
                                                                    <div className="space-y-4">
                                                                      {Object.entries(
                                                                        child
                                                                      ).map(
                                                                        ([
                                                                          key,
                                                                          value,
                                                                        ]) => {
                                                                          if (
                                                                            [
                                                                              "id",
                                                                              "Title",
                                                                              "Type",
                                                                              "__typename",
                                                                            ].includes(
                                                                              key
                                                                            )
                                                                          )
                                                                            return null;

                                                                          return renderField(
                                                                            key,
                                                                            value,
                                                                            uiMetaData
                                                                          );
                                                                        }
                                                                      )}
                                                                    </div>
                                                                  </div>
                                                                )
                                                              )}
                                                            </div>
                                                          </div>
                                                        )}

                                                      {/* Relationships */}
                                                      {modification.new_relationships &&
                                                        modification
                                                          .new_relationships
                                                          .length > 0 && (
                                                          <div className="mt-8">
                                                            <h3 className="typography-heading-4 text-blue-800 mb-4">
                                                              New Relationships
                                                            </h3>
                                                            <div className="space-y-6 p-4 bg-blue-50 rounded-lg">
                                                              {Object.entries(
                                                                groupRelationshipsByType(
                                                                  modification.new_relationships
                                                                )
                                                              ).map(
                                                                (
                                                                  [
                                                                    type,
                                                                    relationships,
                                                                  ],
                                                                  index
                                                                ) => (
                                                                  <div
                                                                    key={index}
                                                                    className="space-y-4"
                                                                  >
                                                                    <h4 className="typography-body font-weight-semibold text-blue-800">
                                                                      {getTitleForRelationship(
                                                                        type
                                                                      )}{" "}
                                                                      (
                                                                      {
                                                                        relationships.length
                                                                      }
                                                                      )
                                                                    </h4>
                                                                    <div className="space-y-4 pl-4">
                                                                      {relationships.map(
                                                                        (
                                                                          relationship,
                                                                          relIndex
                                                                        ) => (
                                                                          <div
                                                                            key={
                                                                              relIndex
                                                                            }
                                                                            className="p-4 bg-white rounded-lg border border-blue-200"
                                                                          >
                                                                            {Object.entries(
                                                                              relationship
                                                                            ).map(
                                                                              ([
                                                                                key,
                                                                                value,
                                                                              ]) => (
                                                                                <div
                                                                                  key={
                                                                                    key
                                                                                  }
                                                                                  className="mb-2"
                                                                                >
                                                                                  <span className="font-weight-bold text-gray-700">
                                                                                    {
                                                                                      key
                                                                                    }
                                                                                    :{" "}
                                                                                  </span>
                                                                                  <span className="text-gray-600">
                                                                                    {typeof value ===
                                                                                      "object"
                                                                                      ? JSON.stringify(
                                                                                        value
                                                                                      )
                                                                                      : value}
                                                                                  </span>
                                                                                </div>
                                                                              )
                                                                            )}
                                                                          </div>
                                                                        )
                                                                      )}
                                                                    </div>
                                                                  </div>
                                                                )
                                                              )}
                                                            </div>
                                                          </div>
                                                        )}

                                                      <div className="mt-5">
                                                        <ActionButtons
                                                          index={index}
                                                          modificationFeedback={
                                                            modification.modification_feedback
                                                          }
                                                        />
                                                      </div>
                                                      <div ref={bottomRef} />
                                                    </div>
                                                  </div>
                                                </AccordionContent>
                                              </AccordionItem>
                                            </Accordion>
                                          );
                                        }
                                      )}
                                      <div ref={bottomRef} />
                                    </div>
                                  </div>
                                ) : (
                                  <Overview />
                                )}
                              </>
                            )}

                            {activeTab === "Code" && <Code />}
                            {/* {activeTab === "Terminal" && <Terminal />}
                            {activeTab === "Steps" && <Steps />}
                            {activeTab === "Infrastructure Visualisation" && (
                              <div>Infrastructure Visualisation Content</div> */}
                            {/* )} */}
                            {activeTab === "User Input" && <UserInput />}
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="sticky top-0 bg-white  border-gray-200 z-10">
                      <div className="flex justify-end items-center space-x-2 -mt-2  py-1">
                        {/* <DynamicButton
                          variant="primary"
                          icon={FaUserPlus}
                          onClick={handleUsersClick}
                          tooltip="Add users to discussion"
                          text="Add Users"
                        /> */}
                        <DynamicButton
                          variant="secondary"
                          icon={isFullScreen ? FaCompress : FaExpand}
                          onClick={onFullScreen}
                          tooltip={
                            isFullScreen
                              ? "Exit fullscreen mode to return to the regular view."
                              : "Enter fullscreen mode for an immersive experience."
                          }
                          size="sqDefault"
                        />
                        <DynamicButton
                          variant="secondary"
                          icon={FaTimes}
                          onClick={() => setShowCloseModal(true)}
                          tooltip="Click here to close this window"
                          size="sqDefault"
                          className="bg-[#f5f6f7]"
                        />
                      </div>
                    </div>

                    <div className="flex-1 overflow-y-auto custom-scrollbar">
                      <NodeInfo nodeInfo={nodeInfo} />
                      <div className="border border-gray-200 rounded-md overflow-hidden bg-white mt-3">
                        {modifications.map((modification, index) => {
                          const isLast = index === modifications.length - 1;
                          if (!modification.modified_node) {
                            return null;
                          }
                          return (
                            <Accordion items={modifications} key={index}>
                              <AccordionItem value={index.toString()}>
                                <AccordionTrigger className="text-gray-700 font-weight-semibold px-4 py-3 bg-gray-50 hover:bg-gray-100">
                                  <div className="flex w-full justify-between items-center">
                                    <div className="text-left">
                                      <div className="project-panel-heading">
                                        {modification.modified_node.Title ||
                                          nodeInfo?.node?.Title ||
                                          "Updated Node"}
                                        {modification.modified_node?.Type && (
                                          <Badge
                                            className="bg-blue-100 text-blue-800"
                                            type={
                                              modification.modified_node?.Type
                                            }
                                          />
                                        )}
                                      </div>
                                      <p className="typography-body-sm text-gray-500">
                                        {formatUTCToLocal(
                                          modification.created_at
                                        )}
                                      </p>
                                    </div>

                                    <div className="flex items-center gap-2">
                                      <div className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-green-500 rounded-full animate-ping" />
                                        <a
                                          href="#"
                                          onClick={(e) => {
                                            e.preventDefault();
                                            handleReadyToMergeClick(e, index);
                                          }}
                                          title="Click to expand and scroll to merge changes"
                                          className="text-green-700 hover:underline flex items-center typography-body-sm"
                                        >
                                          <span>Ready to merge</span>
                                          <FaChevronCircleDown className="ml-1 group-hover:animate-bounce" />
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                </AccordionTrigger>
                                <AccordionContent>
                                  <div className="bg-white rounded-lg">
                                    <div className="space-y-6">
                                      {/* Main Content */}
                                      {Object.entries(
                                        modification.modified_node
                                      ).map(([key, value]) => {
                                        if (
                                          [
                                            "id",
                                            "Title",
                                            "Type",
                                            "__typename",
                                          ].includes(key)
                                        )
                                          return null;

                                        return renderField(
                                          key,
                                          value,
                                          uiMetaData
                                        );
                                      })}

                                      {/* New Child Nodes */}
                                      {modification.new_child_nodes &&
                                        modification.new_child_nodes.length >
                                        0 && (
                                          <div className="mt-8">
                                            <h3 className="typography-heading-4 text-gray-800 mb-4">
                                              New Child Nodes
                                            </h3>
                                            <div className="space-y-6">
                                              {modification.new_child_nodes.map(
                                                (child, index) => (
                                                  <div
                                                    key={index}
                                                    className="p-4 bg-gray-50 rounded-lg border border-gray-200"
                                                  >
                                                    <h4 className="typography-body font-weight-semibold text-gray-700 mb-3">
                                                      {child.Title}
                                                      {child.Type && (
                                                        <Badge
                                                          className="ml-2 bg-blue-100 text-blue-800"
                                                          type={child.Type}
                                                        />
                                                      )}
                                                    </h4>
                                                    <div className="space-y-4">
                                                      {Object.entries(
                                                        child
                                                      ).map(([key, value]) => {
                                                        if (
                                                          [
                                                            "id",
                                                            "Title",
                                                            "Type",
                                                            "__typename",
                                                          ].includes(key)
                                                        )
                                                          return null;

                                                        return renderField(
                                                          key,
                                                          value,
                                                          uiMetaData
                                                        );
                                                      })}
                                                    </div>
                                                  </div>
                                                )
                                              )}
                                            </div>
                                          </div>
                                        )}

                                      {/* Modified Child Nodes */}
                                      {modification.modified_children &&
                                        modification.modified_children.length >
                                        0 && (
                                          <div className="mt-8">
                                            <h3 className="typography-heading-4 text-gray-800 mb-4">
                                              Modified Child Nodes
                                            </h3>
                                            <div className="space-y-6">
                                              {modification.modified_children.map(
                                                (child, index) => (
                                                  <div
                                                    key={index}
                                                    className="p-4 bg-gray-50 rounded-lg border border-gray-200"
                                                  >
                                                    <h4 className="typography-body font-weight-semibold text-gray-700 mb-3">
                                                      {child.Title}
                                                      {child.Type && (
                                                        <Badge
                                                          className="ml-2 bg-blue-100 text-blue-800"
                                                          type={child.Type}
                                                        />
                                                      )}
                                                    </h4>
                                                    <div className="space-y-4">
                                                      {Object.entries(
                                                        child
                                                      ).map(([key, value]) => {
                                                        if (
                                                          [
                                                            "id",
                                                            "Title",
                                                            "Type",
                                                            "__typename",
                                                          ].includes(key)
                                                        )
                                                          return null;

                                                        return renderField(
                                                          key,
                                                          value,
                                                          uiMetaData
                                                        );
                                                      })}
                                                    </div>
                                                  </div>
                                                )
                                              )}
                                            </div>
                                          </div>
                                        )}

                                      {/* Relationships */}
                                      {modification.new_relationships &&
                                        modification.new_relationships.length >
                                        0 && (
                                          <div className="mt-8">
                                            <h3 className="typography-heading-4 text-blue-800 mb-4">
                                              New Relationships
                                            </h3>
                                            <div className="space-y-6 p-4 bg-blue-50 rounded-lg">
                                              {Object.entries(
                                                groupRelationshipsByType(
                                                  modification.new_relationships
                                                )
                                              ).map(
                                                (
                                                  [type, relationships],
                                                  index
                                                ) => (
                                                  <div
                                                    key={index}
                                                    className="space-y-4"
                                                  >
                                                    <h4 className="typography-body font-weight-semibold text-blue-800">
                                                      {getTitleForRelationship(
                                                        type
                                                      )}{" "}
                                                      ({relationships.length})
                                                    </h4>
                                                    <div className="space-y-4 pl-4">
                                                      {relationships.map(
                                                        (
                                                          relationship,
                                                          relIndex
                                                        ) => (
                                                          <div
                                                            key={relIndex}
                                                            className="p-4 bg-white rounded-lg border border-blue-200"
                                                          >
                                                            {Object.entries(
                                                              relationship
                                                            ).map(
                                                              ([
                                                                key,
                                                                value,
                                                              ]) => (
                                                                <div
                                                                  key={key}
                                                                  className="mb-2"
                                                                >
                                                                  <span className="font-weight-bold text-gray-700">
                                                                    {key}:{" "}
                                                                  </span>
                                                                  <span className="text-gray-600">
                                                                    {typeof value ===
                                                                      "object"
                                                                      ? JSON.stringify(
                                                                        value
                                                                      )
                                                                      : value}
                                                                  </span>
                                                                </div>
                                                              )
                                                            )}
                                                          </div>
                                                        )
                                                      )}
                                                    </div>
                                                  </div>
                                                )
                                              )}
                                            </div>
                                          </div>
                                        )}

                                      <div className="mt-5">
                                        <ActionButtons
                                          index={index}
                                          modificationFeedback={
                                            modification.modification_feedback
                                          }
                                        />
                                      </div>
                                      <div ref={bottomRef} />
                                    </div>
                                  </div>
                                </AccordionContent>
                              </AccordionItem>
                            </Accordion>
                          );
                        })}
                        <div ref={bottomRef} />
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {showCloseModal && (
        <CloseModal
          onClose={() => setShowCloseModal(false)}
          onConfirm={() => handleClose(false)}
          setDiscussionId={setDiscussionId}
          pathname={pathname}
          showAlert={showAlert}
        />
      )}
    </>
  );

  function ActionButtons({ index, modificationFeedback }) {
    const [isApproversListModalOpen, setIsApproversListModalOpen] =
      useState(false);

    const [modificationStatus, setModificationStatus] = useState(
      modificationFeedback?.status || "idle"
    );
    const userId = Cookies.get("userId");

    const handleOnApprovalRequested = async (approver) => {
      setModifications((prevModifications) => {
        const updatedModifications = [...prevModifications];
        updatedModifications[index].modification_feedback = {
          status: "Requesting approval",
          approver_id: approver.username,
        };
        return updatedModifications;
      });
      await mergeModificationRequest(
        discussionId,
        index,
        "request",
        approver.username
      );
      setModifications((prevModifications) => {
        const updatedModifications = [...prevModifications];
        updatedModifications[index].modification_feedback = {
          status: "pending",
          approver_id: approver.username,
        };
        return updatedModifications;
      });
      showAlert("Approval requested successfully", "success");
    };

    const handleAddComment = (comment) => {
      // Handle adding comment to your data store
    };


    return (
      <>
        <div className="block">
          <div className="flex mt-2 justify-between">
            <div className="flex space-x-2 mr-4">
              <BootstrapTooltip
                title="Finalize and save your project details"
                placement="bottom"
              >
                <button
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed flex items-center justify-center space-x-2 "
                  onClick={() => {

                    mergeCloseFunction(nodeId, modifications[0].modified_node.Title, mergeStep,
                      mergeStepIndex, index)
                  }}
                  disabled={isMergeLoading}
                >
                  {isMergeLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      <span>Applying changes</span>
                    </>
                  ) : (
                    <span>Merge</span>
                  )}
                </button>
              </BootstrapTooltip>
            </div>
            <ModificationActionButtons
              modificationFeedback={modificationFeedback}
              modificationStatus={modificationStatus}
              allUsers={allUsers}
              showAlert={showAlert}
              setIsApproversListModalOpen={setIsApproversListModalOpen}
              userId={userId}
              setModifications={setModifications}
              discussionId={discussionId}
              index={index}
              mergeModificationRequest={mergeModificationRequest}
              modifications={modifications}
              handleAddComment={handleAddComment}
              handleClose={handleClose}
            />
          </div>
        </div>
        {isApproversListModalOpen && (
          <ApproversListModal
            discussionId={discussionId}
            onClose={() => setIsApproversListModalOpen(false)}
            onApprovalRequested={handleOnApprovalRequested}
          />
        )}
      </>
    );
  }
}

export default function DiscussionModal() {
  return <Modal />;
}
